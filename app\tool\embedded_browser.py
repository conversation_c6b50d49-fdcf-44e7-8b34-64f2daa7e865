#!/usr/bin/env python3
# coding: utf-8
"""
Embedded Browser Tool - OpenManus
Web sitesi içinde gömülü tarayıcı, Chrome penceresi açmaz
"""

import asyncio
import json
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

try:
    import requests
    from bs4 import BeautifulSoup

    BROWSER_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Browser kütüphaneleri yüklü de<PERSON>il: {e}")
    BROWSER_AVAILABLE = False

from app.logger import logger
from app.tool.base import BaseTool
from app.tool.web_search import WebSearch


@dataclass
class BrowserState:
    """Browser durumu"""

    current_url: str = ""
    title: str = ""
    content: str = ""
    status: str = "ready"
    history: List[str] = None

    def __post_init__(self):
        if self.history is None:
            self.history = []


class EmbeddedBrowserTool(BaseTool):
    """Embedded browser aracı - Chrome penceresi açmaz"""

    name: str = "embedded_browser"
    description: str = (
        "Web sitesi içinde gömülü tarayıcı, Chrome penceresi açmadan web araştırması yapar"
    )
    parameters: dict = {
        "type": "object",
        "properties": {
            "action": {
                "type": "string",
                "description": "Yapılacak işlem: search, navigate, extract, back, forward",
                "enum": ["search", "navigate", "extract", "back", "forward"],
            },
            "query": {"type": "string", "description": "Arama sorgusu"},
            "url": {"type": "string", "description": "Gidilecek URL"},
            "goal": {"type": "string", "description": "İçerik çıkarma hedefi"},
        },
        "required": ["action"],
    }

    def __init__(self, websocket_callback=None):
        super().__init__()
        # Instance variables
        object.__setattr__(self, "browser_state", BrowserState())
        object.__setattr__(self, "web_search", WebSearch())
        object.__setattr__(self, "session", requests.Session())
        object.__setattr__(self, "websocket_callback", websocket_callback)

        # User agent ayarla
        self.session.headers.update(
            {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            }
        )

    async def _send_websocket_message(self, message_type: str, data: Dict[str, Any]):
        """WebSocket mesajı gönder"""
        if self.websocket_callback:
            try:
                import json

                message = json.dumps({"type": message_type, **data})
                await self.websocket_callback(message)
            except Exception as e:
                logger.error(f"WebSocket mesaj gönderme hatası: {e}")

    async def search_web(self, query: str) -> Dict[str, Any]:
        """Web araması yap"""
        if not BROWSER_AVAILABLE:
            return {"error": "📦 Browser kütüphaneleri yüklenmemiş"}

        try:
            logger.info(f"🔍 Web araması: {query}")

            # Web search tool kullan
            search_results = await self.web_search.execute(query)

            if not search_results:
                return {"error": f"❌ '{query}' için sonuç bulunamadı"}

            # İlk sonuca git
            first_result = search_results[0]
            if isinstance(first_result, dict) and "url" in first_result:
                url = first_result["url"]
                title = first_result.get("title", "")
            elif isinstance(first_result, str):
                url = first_result
                title = ""
            else:
                return {"error": f"❌ Geçersiz arama sonucu: {first_result}"}

            # WebSocket ile URL'yi gönder
            await self._send_websocket_message("browser_url", {"url": url})
            await self._send_websocket_message(
                "search_status", {"message": f"Navigating to {title or url}"}
            )

            # Sayfayı yükle
            navigate_result = await self.navigate_to_url(url)

            result = {
                "success": True,
                "query": query,
                "url": url,
                "title": title,
                "search_results": search_results[:5],  # İlk 5 sonuç
                "content_preview": navigate_result.get("content", "")[:500],
                "type": "web_search",
            }

            logger.info(f"✅ Arama tamamlandı: {url}")
            return result

        except Exception as e:
            error_msg = f"❌ Web araması hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}

    async def navigate_to_url(self, url: str) -> Dict[str, Any]:
        """URL'ye git"""
        if not BROWSER_AVAILABLE:
            return {"error": "📦 Browser kütüphaneleri yüklenmemiş"}

        try:
            logger.info(f"🌐 Navigating to: {url}")

            # URL'yi düzelt
            if not url.startswith(("http://", "https://")):
                url = "https://" + url

            # WebSocket ile URL'yi gönder
            await self._send_websocket_message("browser_url", {"url": url})
            await self._send_websocket_message(
                "search_status", {"message": f"Loading {url}"}
            )

            # Sayfayı yükle
            response = self.session.get(url, timeout=10)
            response.raise_for_status()

            # HTML parse et
            soup = BeautifulSoup(response.content, "html.parser")

            # Title al
            title = soup.title.string if soup.title else "Untitled"

            # Text içeriği al
            # Script ve style taglarını kaldır
            for script in soup(["script", "style"]):
                script.decompose()

            # Text al
            text_content = soup.get_text()

            # Temizle
            lines = (line.strip() for line in text_content.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            content = " ".join(chunk for chunk in chunks if chunk)

            # Browser state güncelle
            self.browser_state.current_url = url
            self.browser_state.title = title
            self.browser_state.content = content
            self.browser_state.status = "loaded"

            # History'ye ekle
            if url not in self.browser_state.history:
                self.browser_state.history.append(url)

            result = {
                "success": True,
                "url": url,
                "title": title,
                "content": content[:2000],  # İlk 2000 karakter
                "content_length": len(content),
                "status_code": response.status_code,
                "type": "navigation",
            }

            logger.info(f"✅ Sayfa yüklendi: {title}")
            return result

        except Exception as e:
            error_msg = f"❌ Navigasyon hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}

    async def extract_content(self, goal: str) -> Dict[str, Any]:
        """İçerik çıkar"""
        try:
            if not self.browser_state.content:
                return {"error": "❌ Önce bir sayfaya gitmelisiniz"}

            # Basit içerik çıkarma
            content = self.browser_state.content

            # Goal'a göre filtreleme
            if goal.lower() in ["reviews", "review", "yorumlar", "yorum"]:
                # Review anahtar kelimelerini ara
                review_keywords = [
                    "review",
                    "rating",
                    "star",
                    "comment",
                    "feedback",
                    "opinion",
                    "yorum",
                    "değerlendirme",
                ]
                relevant_parts = []

                sentences = content.split(".")
                for sentence in sentences:
                    if any(keyword in sentence.lower() for keyword in review_keywords):
                        relevant_parts.append(sentence.strip())

                extracted_content = ". ".join(
                    relevant_parts[:10]
                )  # İlk 10 ilgili cümle

            elif goal.lower() in ["price", "prices", "fiyat", "fiyatlar"]:
                # Fiyat anahtar kelimelerini ara
                price_keywords = ["$", "€", "₺", "price", "cost", "fiyat", "ücret"]
                relevant_parts = []

                sentences = content.split(".")
                for sentence in sentences:
                    if any(keyword in sentence.lower() for keyword in price_keywords):
                        relevant_parts.append(sentence.strip())

                extracted_content = ". ".join(relevant_parts[:5])

            else:
                # Genel özet
                sentences = content.split(".")
                extracted_content = ". ".join(sentences[:20])  # İlk 20 cümle

            result = {
                "success": True,
                "goal": goal,
                "url": self.browser_state.current_url,
                "title": self.browser_state.title,
                "extracted_content": extracted_content,
                "extraction_length": len(extracted_content),
                "type": "content_extraction",
            }

            logger.info(f"✅ İçerik çıkarıldı: {goal}")
            return result

        except Exception as e:
            error_msg = f"❌ İçerik çıkarma hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}

    async def go_back(self) -> Dict[str, Any]:
        """Geri git"""
        try:
            if len(self.browser_state.history) < 2:
                return {"error": "❌ Geri gidilecek sayfa yok"}

            # Son URL'yi kaldır
            self.browser_state.history.pop()

            # Önceki URL'ye git
            previous_url = self.browser_state.history[-1]
            return await self.navigate_to_url(previous_url)

        except Exception as e:
            error_msg = f"❌ Geri gitme hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}

    async def go_forward(self) -> Dict[str, Any]:
        """İleri git (şu anda desteklenmiyor)"""
        return {"error": "❌ İleri gitme özelliği henüz desteklenmiyor"}

    def get_current_state(self) -> Dict[str, Any]:
        """Mevcut browser durumunu al"""
        return {
            "success": True,
            "current_url": self.browser_state.current_url,
            "title": self.browser_state.title,
            "status": self.browser_state.status,
            "history_count": len(self.browser_state.history),
            "content_length": len(self.browser_state.content),
            "type": "browser_state",
        }

    async def execute(self, action: str, **kwargs) -> Dict[str, Any]:
        """Ana execution fonksiyonu"""
        try:
            if action == "search":
                query = kwargs.get("query", "")
                if not query:
                    return {"error": "❌ Arama sorgusu gerekli"}
                return await self.search_web(query)

            elif action == "navigate":
                url = kwargs.get("url", "")
                if not url:
                    return {"error": "❌ URL gerekli"}
                return await self.navigate_to_url(url)

            elif action == "extract":
                goal = kwargs.get("goal", "general summary")
                return await self.extract_content(goal)

            elif action == "back":
                return await self.go_back()

            elif action == "forward":
                return await self.go_forward()

            elif action == "state":
                return self.get_current_state()

            else:
                return {
                    "error": f"❌ Bilinmeyen action: {action}",
                    "available_actions": [
                        "search",
                        "navigate",
                        "extract",
                        "back",
                        "forward",
                        "state",
                    ],
                }

        except Exception as e:
            return {"error": f"❌ Execution hatası: {str(e)}"}


# Global instance will be created when needed
