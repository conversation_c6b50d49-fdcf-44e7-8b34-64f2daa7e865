#!/usr/bin/env python3
# coding: utf-8
"""
Multi-Model AI Desteği - OpenManus Tool
GPT-4, <PERSON>, <PERSON>'yi aynı anda kullana<PERSON>, model kar<PERSON>ılaştırma ve en iyi cevabı seçme
"""

import asyncio
import json
import time
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

try:
    import google.generativeai as genai
    import openai
    from anthropic import Anthropic

    MULTI_MODEL_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Multi-model kütüphaneleri yüklü değil: {e}")
    MULTI_MODEL_AVAILABLE = False

from app.config import config
from app.logger import logger
from app.tool.base import BaseTool


@dataclass
class ModelResponse:
    """Model yanıt verisi"""

    model_name: str
    response: str
    response_time: float
    token_count: int
    cost_estimate: float
    quality_score: float = 0.0
    error: Optional[str] = None


class MultiModelTool(BaseTool):
    """Multi-model AI desteği aracı"""

    name: str = "multi_model"
    description: str = (
        "Birden fazla AI modelini aynı anda kullanır, kar<PERSON><PERSON>laştırır ve en iyi cevabı seçer"
    )
    parameters: dict = {
        "type": "object",
        "properties": {
            "action": {
                "type": "string",
                "description": "Yapılacak işlem: compare, best_response, analyze_models",
                "enum": ["compare", "best_response", "analyze_models"],
            },
            "prompt": {
                "type": "string",
                "description": "AI modellerine gönderilecek prompt",
            },
            "models": {
                "type": "array",
                "items": {"type": "string"},
                "description": "Kullanılacak modeller: gpt-4, claude-3, gemini-pro",
            },
            "max_tokens": {
                "type": "integer",
                "description": "Maksimum token sayısı",
                "default": 1000,
            },
            "temperature": {
                "type": "number",
                "description": "Yaratıcılık seviyesi (0.0-1.0)",
                "default": 0.7,
            },
        },
        "required": ["action", "prompt"],
    }

    def __init__(self):
        super().__init__()
        # Instance variables
        object.__setattr__(self, "clients", {})
        object.__setattr__(self, "model_configs", {})
        object.__setattr__(self, "response_history", [])
        self._initialize_clients()

    def _initialize_clients(self):
        """AI model istemcilerini başlat"""
        if not MULTI_MODEL_AVAILABLE:
            return

        try:
            # OpenAI GPT-4
            if hasattr(config, "openai_api_key") and config.openai_api_key:
                self.clients["gpt-4"] = openai.OpenAI(api_key=config.openai_api_key)
                self.model_configs["gpt-4"] = {
                    "model": "gpt-4",
                    "cost_per_1k_tokens": 0.03,
                    "max_tokens": 8192,
                }

            # Anthropic Claude
            if hasattr(config, "anthropic_api_key") and config.anthropic_api_key:
                self.clients["claude-3"] = Anthropic(api_key=config.anthropic_api_key)
                self.model_configs["claude-3"] = {
                    "model": "claude-3-sonnet-20240229",
                    "cost_per_1k_tokens": 0.015,
                    "max_tokens": 4096,
                }

            # Google Gemini
            if hasattr(config, "google_api_key") and config.google_api_key:
                genai.configure(api_key=config.google_api_key)
                self.clients["gemini-pro"] = genai.GenerativeModel("gemini-pro")
                self.model_configs["gemini-pro"] = {
                    "model": "gemini-pro",
                    "cost_per_1k_tokens": 0.001,
                    "max_tokens": 2048,
                }

            logger.info(
                f"🤖 Multi-model clients initialized: {list(self.clients.keys())}"
            )

        except Exception as e:
            logger.error(f"❌ Multi-model client initialization error: {e}")

    async def compare_models(
        self,
        prompt: str,
        models: List[str],
        max_tokens: int = 1000,
        temperature: float = 0.7,
    ) -> Dict[str, Any]:
        """Birden fazla modeli karşılaştır"""
        if not MULTI_MODEL_AVAILABLE:
            return {"error": "📦 Multi-model kütüphaneleri yüklenmemiş"}

        available_models = [m for m in models if m in self.clients]
        if not available_models:
            return {
                "error": f"❌ Hiçbir model mevcut değil. Yapılandırılmış modeller: {list(self.clients.keys())}"
            }

        responses = []
        tasks = []

        # Tüm modelleri paralel olarak çağır
        for model_name in available_models:
            task = self._call_model(model_name, prompt, max_tokens, temperature)
            tasks.append(task)

        # Tüm yanıtları bekle
        model_responses = await asyncio.gather(*tasks, return_exceptions=True)

        for i, response in enumerate(model_responses):
            if isinstance(response, Exception):
                responses.append(
                    ModelResponse(
                        model_name=available_models[i],
                        response="",
                        response_time=0.0,
                        token_count=0,
                        cost_estimate=0.0,
                        error=str(response),
                    )
                )
            else:
                responses.append(response)

        # Kalite skorlarını hesapla
        self._calculate_quality_scores(responses)

        # Sonuçları kaydet
        comparison_result = {
            "success": True,
            "prompt": prompt,
            "models_used": available_models,
            "responses": [self._response_to_dict(r) for r in responses],
            "best_model": self._find_best_model(responses),
            "summary": self._generate_summary(responses),
        }

        self.response_history.append(comparison_result)
        return comparison_result

    async def _call_model(
        self, model_name: str, prompt: str, max_tokens: int, temperature: float
    ) -> ModelResponse:
        """Tek bir modeli çağır"""
        start_time = time.time()

        try:
            if model_name == "gpt-4":
                response = await self._call_openai(prompt, max_tokens, temperature)
            elif model_name == "claude-3":
                response = await self._call_claude(prompt, max_tokens, temperature)
            elif model_name == "gemini-pro":
                response = await self._call_gemini(prompt, max_tokens, temperature)
            else:
                raise ValueError(f"Desteklenmeyen model: {model_name}")

            response_time = time.time() - start_time
            token_count = len(response.split()) * 1.3  # Yaklaşık token sayısı
            cost_estimate = (token_count / 1000) * self.model_configs[model_name][
                "cost_per_1k_tokens"
            ]

            return ModelResponse(
                model_name=model_name,
                response=response,
                response_time=response_time,
                token_count=int(token_count),
                cost_estimate=cost_estimate,
            )

        except Exception as e:
            return ModelResponse(
                model_name=model_name,
                response="",
                response_time=time.time() - start_time,
                token_count=0,
                cost_estimate=0.0,
                error=str(e),
            )

    async def _call_openai(
        self, prompt: str, max_tokens: int, temperature: float
    ) -> str:
        """OpenAI GPT-4 çağrısı"""
        client = self.clients["gpt-4"]
        response = await asyncio.to_thread(
            client.chat.completions.create,
            model="gpt-4",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=max_tokens,
            temperature=temperature,
        )
        return response.choices[0].message.content

    async def _call_claude(
        self, prompt: str, max_tokens: int, temperature: float
    ) -> str:
        """Anthropic Claude çağrısı"""
        client = self.clients["claude-3"]
        response = await asyncio.to_thread(
            client.messages.create,
            model="claude-3-sonnet-20240229",
            max_tokens=max_tokens,
            temperature=temperature,
            messages=[{"role": "user", "content": prompt}],
        )
        return response.content[0].text

    async def _call_gemini(
        self, prompt: str, max_tokens: int, temperature: float
    ) -> str:
        """Google Gemini çağrısı"""
        model = self.clients["gemini-pro"]
        response = await asyncio.to_thread(
            model.generate_content,
            prompt,
            generation_config=genai.types.GenerationConfig(
                max_output_tokens=max_tokens, temperature=temperature
            ),
        )
        return response.text

    def _calculate_quality_scores(self, responses: List[ModelResponse]):
        """Yanıt kalite skorlarını hesapla"""
        for response in responses:
            if response.error:
                response.quality_score = 0.0
                continue

            # Basit kalite skoru hesaplama
            score = 0.0

            # Uzunluk skoru (çok kısa veya çok uzun değil)
            length = len(response.response)
            if 50 <= length <= 2000:
                score += 30
            elif length > 20:
                score += 15

            # Hız skoru (daha hızlı daha iyi)
            if response.response_time < 2.0:
                score += 25
            elif response.response_time < 5.0:
                score += 15
            elif response.response_time < 10.0:
                score += 5

            # Maliyet skoru (daha ucuz daha iyi)
            if response.cost_estimate < 0.01:
                score += 25
            elif response.cost_estimate < 0.05:
                score += 15
            elif response.cost_estimate < 0.1:
                score += 5

            # İçerik kalitesi (basit heuristikler)
            if any(
                word in response.response.lower()
                for word in ["çünkü", "because", "örneğin", "for example"]
            ):
                score += 10
            if len(response.response.split(".")) > 2:  # Birden fazla cümle
                score += 10

            response.quality_score = min(score, 100.0)

    def _find_best_model(self, responses: List[ModelResponse]) -> str:
        """En iyi modeli bul"""
        valid_responses = [r for r in responses if not r.error]
        if not valid_responses:
            return "none"

        best_response = max(valid_responses, key=lambda r: r.quality_score)
        return best_response.model_name

    def _generate_summary(self, responses: List[ModelResponse]) -> Dict[str, Any]:
        """Karşılaştırma özeti oluştur"""
        valid_responses = [r for r in responses if not r.error]

        if not valid_responses:
            return {"error": "Hiçbir model başarılı yanıt veremedi"}

        avg_time = sum(r.response_time for r in valid_responses) / len(valid_responses)
        total_cost = sum(r.cost_estimate for r in valid_responses)
        avg_quality = sum(r.quality_score for r in valid_responses) / len(
            valid_responses
        )

        return {
            "models_compared": len(responses),
            "successful_responses": len(valid_responses),
            "average_response_time": round(avg_time, 2),
            "total_cost_estimate": round(total_cost, 4),
            "average_quality_score": round(avg_quality, 1),
            "fastest_model": min(
                valid_responses, key=lambda r: r.response_time
            ).model_name,
            "cheapest_model": min(
                valid_responses, key=lambda r: r.cost_estimate
            ).model_name,
            "highest_quality": max(
                valid_responses, key=lambda r: r.quality_score
            ).model_name,
        }

    def _response_to_dict(self, response: ModelResponse) -> Dict[str, Any]:
        """ModelResponse'u dict'e çevir"""
        return {
            "model_name": response.model_name,
            "response": response.response,
            "response_time": round(response.response_time, 2),
            "token_count": response.token_count,
            "cost_estimate": round(response.cost_estimate, 4),
            "quality_score": round(response.quality_score, 1),
            "error": response.error,
        }

    async def get_best_response(
        self,
        prompt: str,
        models: List[str],
        max_tokens: int = 1000,
        temperature: float = 0.7,
    ) -> Dict[str, Any]:
        """En iyi yanıtı al"""
        comparison = await self.compare_models(prompt, models, max_tokens, temperature)

        if not comparison.get("success"):
            return comparison

        best_model = comparison["best_model"]
        if best_model == "none":
            return {"error": "❌ Hiçbir model başarılı yanıt veremedi"}

        best_response = next(
            r for r in comparison["responses"] if r["model_name"] == best_model
        )

        return {
            "success": True,
            "best_model": best_model,
            "response": best_response["response"],
            "quality_score": best_response["quality_score"],
            "response_time": best_response["response_time"],
            "cost_estimate": best_response["cost_estimate"],
            "comparison_summary": comparison["summary"],
        }

    async def analyze_models(self) -> Dict[str, Any]:
        """Model performans analizi"""
        if not self.response_history:
            return {"error": "❌ Henüz karşılaştırma geçmişi yok"}

        # Geçmiş verilerden istatistikler
        model_stats = {}

        for comparison in self.response_history:
            for response in comparison["responses"]:
                model_name = response["model_name"]
                if model_name not in model_stats:
                    model_stats[model_name] = {
                        "total_calls": 0,
                        "successful_calls": 0,
                        "total_time": 0.0,
                        "total_cost": 0.0,
                        "total_quality": 0.0,
                        "errors": 0,
                    }

                stats = model_stats[model_name]
                stats["total_calls"] += 1

                if not response.get("error"):
                    stats["successful_calls"] += 1
                    stats["total_time"] += response["response_time"]
                    stats["total_cost"] += response["cost_estimate"]
                    stats["total_quality"] += response["quality_score"]
                else:
                    stats["errors"] += 1

        # Ortalama değerleri hesapla
        for model_name, stats in model_stats.items():
            if stats["successful_calls"] > 0:
                stats["avg_response_time"] = round(
                    stats["total_time"] / stats["successful_calls"], 2
                )
                stats["avg_cost"] = round(
                    stats["total_cost"] / stats["successful_calls"], 4
                )
                stats["avg_quality"] = round(
                    stats["total_quality"] / stats["successful_calls"], 1
                )
                stats["success_rate"] = round(
                    stats["successful_calls"] / stats["total_calls"] * 100, 1
                )
            else:
                stats["avg_response_time"] = 0.0
                stats["avg_cost"] = 0.0
                stats["avg_quality"] = 0.0
                stats["success_rate"] = 0.0

        return {
            "success": True,
            "total_comparisons": len(self.response_history),
            "model_statistics": model_stats,
            "available_models": list(self.clients.keys()),
            "recommendations": self._generate_recommendations(model_stats),
        }

    def _generate_recommendations(self, model_stats: Dict) -> Dict[str, str]:
        """Model önerileri oluştur"""
        if not model_stats:
            return {}

        recommendations = {}

        # En hızlı model
        fastest = min(
            model_stats.items(),
            key=lambda x: (
                x[1]["avg_response_time"]
                if x[1]["avg_response_time"] > 0
                else float("inf")
            ),
        )
        recommendations["fastest"] = (
            f"{fastest[0]} ({fastest[1]['avg_response_time']}s)"
        )

        # En ucuz model
        cheapest = min(
            model_stats.items(),
            key=lambda x: x[1]["avg_cost"] if x[1]["avg_cost"] > 0 else float("inf"),
        )
        recommendations["cheapest"] = f"{cheapest[0]} (${cheapest[1]['avg_cost']})"

        # En kaliteli model
        highest_quality = max(model_stats.items(), key=lambda x: x[1]["avg_quality"])
        recommendations["highest_quality"] = (
            f"{highest_quality[0]} ({highest_quality[1]['avg_quality']}/100)"
        )

        # En güvenilir model
        most_reliable = max(model_stats.items(), key=lambda x: x[1]["success_rate"])
        recommendations["most_reliable"] = (
            f"{most_reliable[0]} ({most_reliable[1]['success_rate']}%)"
        )

        return recommendations

    async def execute(self, action: str, **kwargs) -> Dict[str, Any]:
        """Ana execution fonksiyonu"""
        try:
            if action == "compare":
                return await self.compare_models(
                    kwargs.get("prompt", ""),
                    kwargs.get("models", ["gpt-4", "claude-3", "gemini-pro"]),
                    kwargs.get("max_tokens", 1000),
                    kwargs.get("temperature", 0.7),
                )
            elif action == "best_response":
                return await self.get_best_response(
                    kwargs.get("prompt", ""),
                    kwargs.get("models", ["gpt-4", "claude-3", "gemini-pro"]),
                    kwargs.get("max_tokens", 1000),
                    kwargs.get("temperature", 0.7),
                )
            elif action == "analyze_models":
                return await self.analyze_models()
            else:
                return {
                    "error": f"❌ Bilinmeyen action: {action}",
                    "available_actions": ["compare", "best_response", "analyze_models"],
                }
        except Exception as e:
            return {"error": f"❌ Execution hatası: {str(e)}"}


# Global instance will be created when needed
