#!/usr/bin/env python3
# coding: utf-8
"""
OpenManus Web Interface
Web tabanlı kullanıcı arayüzü ile OpenManus'u kullanın
"""

from datetime import datetime

import uvicorn
from fastapi import FastAPI, Form
from fastapi.responses import HTMLResponse, JSONResponse

# OpenManus imports
from app.agent.manus import Manus
from app.logger import logger

app = FastAPI(title="OpenManus Web Interface")

# Global değişkenler
current_agent = None
chat_history = []


def get_agent():
    """Agent instance'ını al veya oluştur"""
    global current_agent
    if current_agent is None:
        try:
            current_agent = Manus()
            logger.info("Agent başarıyla oluşturuldu")
        except Exception as e:
            logger.error(f"Agent oluşturulurken hata: {e}")
            current_agent = None
    return current_agent


@app.get("/", response_class=HTMLResponse)
async def home():
    """Ana sayfa"""
    html_content = """
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenManus Web Interface</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 90%;
            max-width: 800px;
            height: 80vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }
        .header h1 { font-size: 24px; margin-bottom: 5px; }
        .header p { opacity: 0.9; font-size: 14px; }
        .clear-button {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
        }
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }
        .message.user { justify-content: flex-end; }
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        .message.user .message-content {
            background: #667eea;
            color: white;
        }
        .message.agent .message-content {
            background: white;
            border: 1px solid #e0e0e0;
            color: #333;
        }
        .message.error .message-content {
            background: #ffebee;
            border: 1px solid #ffcdd2;
            color: #c62828;
        }
        .timestamp {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 5px;
        }
        .input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }
        .input-form {
            display: flex;
            gap: 10px;
        }
        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s;
        }
        .message-input:focus { border-color: #667eea; }
        .send-button {
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: transform 0.2s;
        }
        .send-button:hover { transform: translateY(-2px); }
        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 10px;
            color: #666;
        }
        .loading.show { display: block; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button class="clear-button" onclick="clearChat()">🗑️ Temizle</button>
            <h1>🤖 OpenManus</h1>
            <p>Web tabanlı AI asistan arayüzü</p>
        </div>

        <div class="chat-container">
            <div class="chat-messages" id="chatMessages">
                <div class="message agent">
                    <div class="message-content">
                        👋 Merhaba! Ben OpenManus AI asistanınızım. Size nasıl yardımcı olabilirim?
                        <br><br>
                        💡 Örnekler:
                        <br>• "Python ile bir hesap makinesi yaz"
                        <br>• "Web'de Python öğrenme kaynakları ara"
                        <br>• "Bugünün hava durumunu kontrol et"
                        <div class="timestamp">Şimdi</div>
                    </div>
                </div>
            </div>

            <div class="loading" id="loading">🤔 Düşünüyorum...</div>

            <div class="input-container">
                <form class="input-form" onsubmit="sendMessage(event)">
                    <input type="text" name="message" class="message-input"
                           placeholder="Mesajınızı yazın..." required id="messageInput">
                    <button type="submit" class="send-button" id="sendButton">📤 Gönder</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        async function sendMessage(event) {
            event.preventDefault();

            const messageInput = document.getElementById('messageInput');
            const sendButton = document.getElementById('sendButton');
            const loading = document.getElementById('loading');

            const message = messageInput.value.trim();
            if (!message) return;

            sendButton.disabled = true;
            loading.classList.add('show');
            messageInput.value = '';

            addMessage('user', message);

            try {
                const formData = new FormData();
                formData.append('message', message);

                const response = await fetch('/chat', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    addMessage('agent', data.response);
                } else {
                    addMessage('error', data.error || 'Bir hata oluştu');
                }

            } catch (error) {
                addMessage('error', 'Bağlantı hatası: ' + error.message);
            } finally {
                sendButton.disabled = false;
                loading.classList.remove('show');
                messageInput.focus();
            }
        }

        function addMessage(type, content) {
            const chatMessages = document.getElementById('chatMessages');
            const timestamp = new Date().toLocaleTimeString('tr-TR',
                {hour: '2-digit', minute: '2-digit', second: '2-digit'});

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = `
                <div class="message-content">
                    ${content}
                    <div class="timestamp">${timestamp}</div>
                </div>
            `;

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        async function clearChat() {
            if (confirm('Chat geçmişini temizlemek istediğinizden emin misiniz?')) {
                try {
                    await fetch('/clear');
                    location.reload();
                } catch (error) {
                    alert('Temizleme sırasında hata oluştu');
                }
            }
        }

        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                document.querySelector('.input-form').dispatchEvent(new Event('submit'));
            }
        });

        window.onload = function() {
            document.getElementById('messageInput').focus();
        };
    </script>
</body>
</html>
    """
    return HTMLResponse(content=html_content)


@app.post("/chat")
async def chat(message: str = Form(...)):
    """Chat endpoint - kullanıcı mesajını işle"""
    global chat_history

    if not message.strip():
        return JSONResponse({"error": "Mesaj boş olamaz"}, status_code=400)

    # Kullanıcı mesajını geçmişe ekle
    user_message = {
        "type": "user",
        "content": message,
        "timestamp": datetime.now().strftime("%H:%M:%S"),
    }
    chat_history.append(user_message)

    try:
        # Agent'ı al
        agent = get_agent()
        if agent is None:
            error_msg = "Agent başlatılamadı. Lütfen konfigürasyonu kontrol edin."
            chat_history.append(
                {
                    "type": "error",
                    "content": error_msg,
                    "timestamp": datetime.now().strftime("%H:%M:%S"),
                }
            )
            return JSONResponse({"error": error_msg}, status_code=500)

        # Mesajı işle
        logger.info(f"Kullanıcı mesajı işleniyor: {message}")

        # Agent'ın cevabını al
        response = await process_message_with_agent(agent, message)

        # Agent cevabını geçmişe ekle
        agent_message = {
            "type": "agent",
            "content": response,
            "timestamp": datetime.now().strftime("%H:%M:%S"),
        }
        chat_history.append(agent_message)

        return JSONResponse(
            {"success": True, "response": response, "chat_history": chat_history}
        )

    except Exception as e:
        error_msg = f"Mesaj işlenirken hata oluştu: {str(e)}"
        logger.error(error_msg)

        chat_history.append(
            {
                "type": "error",
                "content": error_msg,
                "timestamp": datetime.now().strftime("%H:%M:%S"),
            }
        )

        return JSONResponse({"error": error_msg}, status_code=500)


async def process_message_with_agent(agent, message):
    """Agent ile mesajı işle"""
    try:
        # Agent'ın run metodunu çağır
        result = await agent.run(message)

        if result:
            return str(result)
        else:
            return "Üzgünüm, bu konuda size yardımcı olamadım."

    except Exception as e:
        logger.error(f"Agent işleme hatası: {e}")
        return f"İşlem sırasında bir hata oluştu: {str(e)}"


@app.get("/clear")
async def clear_chat():
    """Chat geçmişini temizle"""
    global chat_history
    chat_history = []
    return JSONResponse({"success": True, "message": "Chat geçmişi temizlendi"})


@app.get("/status")
async def status():
    """Sistem durumu"""
    agent = get_agent()
    return JSONResponse(
        {
            "agent_status": "active" if agent else "inactive",
            "chat_history_count": len(chat_history),
            "timestamp": datetime.now().isoformat(),
        }
    )


if __name__ == "__main__":
    print("🌐 OpenManus Web Interface başlatılıyor...")
    print("📱 Tarayıcınızda http://localhost:8000 adresini açın")
    print("🛑 Durdurmak için Ctrl+C tuşlarına basın")

    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
