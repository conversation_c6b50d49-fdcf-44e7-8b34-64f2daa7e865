#!/usr/bin/env python3
# coding: utf-8
"""
Gelişmiş Veri Analizi Araçları
Excel, CSV, JSON dosyalarını işleme, grafik oluşturma ve rapor üretme
"""

import os
import json
import base64
from io import BytesIO
from typing import Dict, List, Any, Optional, Union
from pathlib import Path

try:
    import pandas as pd
    import numpy as np
    import matplotlib.pyplot as plt
    import seaborn as sns
    import plotly.express as px
    import plotly.graph_objects as go
    from plotly.offline import plot
    import openpyxl
    from sklearn.preprocessing import StandardScaler
    from sklearn.cluster import KMeans
    from sklearn.linear_model import LinearRegression
    from scipy import stats
    ANALYSIS_AVAILABLE = True
except ImportError as e:
    print(f"Veri analizi kütüphaneleri yüklü değil: {e}")
    ANALYSIS_AVAILABLE = False

from app.logger import logger


class DataAnalyzer:
    """Gelişmiş veri analizi sınıfı"""
    
    def __init__(self):
        self.data = None
        self.file_path = None
        self.analysis_results = {}
        
    def load_file(self, file_path: str) -> Dict[str, Any]:
        """Dosya yükleme - Excel, CSV, JSON destekli"""
        if not ANALYSIS_AVAILABLE:
            return {"error": "Veri analizi kütüphaneleri yüklü değil"}
            
        try:
            file_path = Path(file_path)
            self.file_path = file_path
            
            if not file_path.exists():
                return {"error": f"Dosya bulunamadı: {file_path}"}
            
            # Dosya türüne göre yükleme
            if file_path.suffix.lower() in ['.xlsx', '.xls']:
                self.data = pd.read_excel(file_path)
            elif file_path.suffix.lower() == '.csv':
                # Encoding otomatik tespiti
                try:
                    self.data = pd.read_csv(file_path, encoding='utf-8')
                except UnicodeDecodeError:
                    self.data = pd.read_csv(file_path, encoding='latin-1')
            elif file_path.suffix.lower() == '.json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                self.data = pd.json_normalize(json_data)
            else:
                return {"error": f"Desteklenmeyen dosya türü: {file_path.suffix}"}
            
            # Temel bilgiler
            info = {
                "success": True,
                "file_name": file_path.name,
                "shape": self.data.shape,
                "columns": list(self.data.columns),
                "data_types": self.data.dtypes.to_dict(),
                "memory_usage": f"{self.data.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB",
                "preview": self.data.head().to_dict('records')
            }
            
            logger.info(f"Dosya başarıyla yüklendi: {file_path.name}")
            return info
            
        except Exception as e:
            error_msg = f"Dosya yükleme hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    def basic_analysis(self) -> Dict[str, Any]:
        """Temel istatistiksel analiz"""
        if self.data is None:
            return {"error": "Önce bir dosya yükleyin"}
        
        try:
            analysis = {
                "shape": self.data.shape,
                "missing_values": self.data.isnull().sum().to_dict(),
                "numeric_summary": self.data.describe().to_dict(),
                "categorical_summary": {},
                "correlations": {}
            }
            
            # Kategorik sütunlar için özet
            categorical_cols = self.data.select_dtypes(include=['object']).columns
            for col in categorical_cols:
                analysis["categorical_summary"][col] = {
                    "unique_count": self.data[col].nunique(),
                    "top_values": self.data[col].value_counts().head().to_dict()
                }
            
            # Sayısal sütunlar için korelasyon
            numeric_cols = self.data.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 1:
                analysis["correlations"] = self.data[numeric_cols].corr().to_dict()
            
            self.analysis_results["basic"] = analysis
            return analysis
            
        except Exception as e:
            error_msg = f"Temel analiz hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    def create_visualization(self, chart_type: str, x_column: str, y_column: str = None, 
                           title: str = None) -> Dict[str, Any]:
        """Grafik oluşturma"""
        if self.data is None:
            return {"error": "Önce bir dosya yükleyin"}
        
        if not ANALYSIS_AVAILABLE:
            return {"error": "Grafik kütüphaneleri yüklü değil"}
        
        try:
            # Grafik türüne göre oluşturma
            if chart_type == "histogram":
                fig = px.histogram(self.data, x=x_column, title=title or f"{x_column} Histogram")
            elif chart_type == "scatter":
                if not y_column:
                    return {"error": "Scatter plot için y_column gerekli"}
                fig = px.scatter(self.data, x=x_column, y=y_column, 
                               title=title or f"{x_column} vs {y_column}")
            elif chart_type == "line":
                fig = px.line(self.data, x=x_column, y=y_column,
                             title=title or f"{x_column} - {y_column} Line Chart")
            elif chart_type == "bar":
                value_counts = self.data[x_column].value_counts()
                fig = px.bar(x=value_counts.index, y=value_counts.values,
                           title=title or f"{x_column} Bar Chart")
            elif chart_type == "box":
                fig = px.box(self.data, y=x_column, title=title or f"{x_column} Box Plot")
            elif chart_type == "correlation_heatmap":
                numeric_data = self.data.select_dtypes(include=[np.number])
                corr_matrix = numeric_data.corr()
                fig = px.imshow(corr_matrix, text_auto=True, aspect="auto",
                              title=title or "Correlation Heatmap")
            else:
                return {"error": f"Desteklenmeyen grafik türü: {chart_type}"}
            
            # HTML olarak kaydet
            chart_html = fig.to_html(include_plotlyjs='cdn')
            
            # Base64 image olarak da kaydet
            img_bytes = fig.to_image(format="png")
            img_base64 = base64.b64encode(img_bytes).decode()
            
            result = {
                "success": True,
                "chart_type": chart_type,
                "html": chart_html,
                "image_base64": img_base64,
                "columns_used": [x_column] + ([y_column] if y_column else [])
            }
            
            logger.info(f"Grafik oluşturuldu: {chart_type}")
            return result
            
        except Exception as e:
            error_msg = f"Grafik oluşturma hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    def advanced_analysis(self, analysis_type: str, **kwargs) -> Dict[str, Any]:
        """Gelişmiş analiz işlemleri"""
        if self.data is None:
            return {"error": "Önce bir dosya yükleyin"}
        
        try:
            if analysis_type == "clustering":
                return self._perform_clustering(**kwargs)
            elif analysis_type == "regression":
                return self._perform_regression(**kwargs)
            elif analysis_type == "outlier_detection":
                return self._detect_outliers(**kwargs)
            elif analysis_type == "time_series":
                return self._analyze_time_series(**kwargs)
            else:
                return {"error": f"Desteklenmeyen analiz türü: {analysis_type}"}
                
        except Exception as e:
            error_msg = f"Gelişmiş analiz hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    def _perform_clustering(self, n_clusters: int = 3, columns: List[str] = None) -> Dict[str, Any]:
        """K-Means clustering analizi"""
        numeric_data = self.data.select_dtypes(include=[np.number])
        if columns:
            numeric_data = numeric_data[columns]
        
        # Veriyi normalize et
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(numeric_data)
        
        # K-Means uygula
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        clusters = kmeans.fit_predict(scaled_data)
        
        # Sonuçları dataframe'e ekle
        result_data = self.data.copy()
        result_data['cluster'] = clusters
        
        # Cluster istatistikleri
        cluster_stats = result_data.groupby('cluster')[numeric_data.columns].mean()
        
        return {
            "success": True,
            "n_clusters": n_clusters,
            "cluster_centers": kmeans.cluster_centers_.tolist(),
            "cluster_stats": cluster_stats.to_dict(),
            "cluster_counts": pd.Series(clusters).value_counts().to_dict()
        }
    
    def _perform_regression(self, x_column: str, y_column: str) -> Dict[str, Any]:
        """Linear regression analizi"""
        if x_column not in self.data.columns or y_column not in self.data.columns:
            return {"error": "Belirtilen sütunlar bulunamadı"}
        
        # Eksik değerleri temizle
        clean_data = self.data[[x_column, y_column]].dropna()
        
        X = clean_data[x_column].values.reshape(-1, 1)
        y = clean_data[y_column].values
        
        # Regression modeli
        model = LinearRegression()
        model.fit(X, y)
        
        # Tahminler
        y_pred = model.predict(X)
        
        # İstatistikler
        r2_score = model.score(X, y)
        correlation = stats.pearsonr(X.flatten(), y)[0]
        
        return {
            "success": True,
            "coefficient": model.coef_[0],
            "intercept": model.intercept_,
            "r2_score": r2_score,
            "correlation": correlation,
            "equation": f"y = {model.coef_[0]:.4f}x + {model.intercept_:.4f}"
        }
    
    def _detect_outliers(self, column: str, method: str = "iqr") -> Dict[str, Any]:
        """Outlier detection"""
        if column not in self.data.columns:
            return {"error": f"Sütun bulunamadı: {column}"}
        
        data_col = self.data[column].dropna()
        
        if method == "iqr":
            Q1 = data_col.quantile(0.25)
            Q3 = data_col.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            outliers = data_col[(data_col < lower_bound) | (data_col > upper_bound)]
        elif method == "zscore":
            z_scores = np.abs(stats.zscore(data_col))
            outliers = data_col[z_scores > 3]
        else:
            return {"error": f"Desteklenmeyen method: {method}"}
        
        return {
            "success": True,
            "method": method,
            "outlier_count": len(outliers),
            "outlier_percentage": len(outliers) / len(data_col) * 100,
            "outlier_values": outliers.tolist()
        }
    
    def _analyze_time_series(self, date_column: str, value_column: str) -> Dict[str, Any]:
        """Zaman serisi analizi"""
        if date_column not in self.data.columns or value_column not in self.data.columns:
            return {"error": "Belirtilen sütunlar bulunamadı"}
        
        # Tarih sütununu datetime'a çevir
        ts_data = self.data.copy()
        ts_data[date_column] = pd.to_datetime(ts_data[date_column])
        ts_data = ts_data.sort_values(date_column)
        
        # Temel istatistikler
        trend = "increasing" if ts_data[value_column].iloc[-1] > ts_data[value_column].iloc[0] else "decreasing"
        volatility = ts_data[value_column].std()
        
        return {
            "success": True,
            "trend": trend,
            "volatility": volatility,
            "min_value": ts_data[value_column].min(),
            "max_value": ts_data[value_column].max(),
            "mean_value": ts_data[value_column].mean()
        }
    
    def generate_report(self, output_format: str = "html") -> Dict[str, Any]:
        """Kapsamlı rapor oluşturma"""
        if self.data is None:
            return {"error": "Önce bir dosya yükleyin"}
        
        try:
            # Temel analiz yap
            basic_stats = self.basic_analysis()
            
            # HTML raporu oluştur
            if output_format == "html":
                html_report = self._generate_html_report(basic_stats)
                return {
                    "success": True,
                    "format": "html",
                    "content": html_report
                }
            else:
                return {"error": f"Desteklenmeyen format: {output_format}"}
                
        except Exception as e:
            error_msg = f"Rapor oluşturma hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    def _generate_html_report(self, basic_stats: Dict) -> str:
        """HTML raporu oluştur"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Veri Analizi Raporu</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .section {{ margin: 20px 0; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>📊 Veri Analizi Raporu</h1>
                <p>Dosya: {self.file_path.name if self.file_path else 'Bilinmiyor'}</p>
                <p>Oluşturulma: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <div class="section">
                <h2>📈 Temel İstatistikler</h2>
                <p>Satır sayısı: {basic_stats.get('shape', [0, 0])[0]}</p>
                <p>Sütun sayısı: {basic_stats.get('shape', [0, 0])[1]}</p>
                <p>Eksik değer sayısı: {sum(basic_stats.get('missing_values', {}).values())}</p>
            </div>
            
            <div class="section">
                <h2>📋 Sütun Bilgileri</h2>
                <table>
                    <tr><th>Sütun Adı</th><th>Veri Tipi</th><th>Eksik Değer</th></tr>
        """
        
        for col in basic_stats.get('missing_values', {}):
            missing = basic_stats['missing_values'][col]
            html += f"<tr><td>{col}</td><td>-</td><td>{missing}</td></tr>"
        
        html += """
                </table>
            </div>
        </body>
        </html>
        """
        
        return html


# Global analyzer instance
analyzer = DataAnalyzer()


def analyze_data_file(file_path: str) -> Dict[str, Any]:
    """Dosya analizi için ana fonksiyon"""
    return analyzer.load_file(file_path)


def create_chart(chart_type: str, x_column: str, y_column: str = None, title: str = None) -> Dict[str, Any]:
    """Grafik oluşturma için ana fonksiyon"""
    return analyzer.create_visualization(chart_type, x_column, y_column, title)


def get_basic_statistics() -> Dict[str, Any]:
    """Temel istatistikler için ana fonksiyon"""
    return analyzer.basic_analysis()


def perform_advanced_analysis(analysis_type: str, **kwargs) -> Dict[str, Any]:
    """Gelişmiş analiz için ana fonksiyon"""
    return analyzer.advanced_analysis(analysis_type, **kwargs)


def generate_analysis_report(output_format: str = "html") -> Dict[str, Any]:
    """Rapor oluşturma için ana fonksiyon"""
    return analyzer.generate_report(output_format)
