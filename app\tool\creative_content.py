#!/usr/bin/env python3
# coding: utf-8
"""
Yaratıcı İçerik Üretimi - OpenManus Tool
DALL-E, Midjourney API'leri ile resim oluşturma, video editing, müzik kompozisyonu
"""

import os
import base64
import requests
from typing import Dict, List, Any, Optional
from pathlib import Path

try:
    import openai
    from PIL import Image, ImageDraw, ImageFont
    import cv2
    import numpy as np
    CREATIVE_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Yaratıcı içerik kütüphaneleri yüklü değil: {e}")
    CREATIVE_AVAILABLE = False

from app.logger import logger
from app.tool.base import BaseTool
from app.config import config


class CreativeContentTool(BaseTool):
    """Yaratıcı içerik üretimi aracı"""
    
    name: str = "creative_content"
    description: str = "Resim oluşturma, video editing, müzik kompozisyonu ve yaratıcı içerik üretimi"
    parameters: dict = {
        "type": "object",
        "properties": {
            "action": {
                "type": "string",
                "description": "Yapılacak işlem: generate_image, edit_image, create_video, compose_music, generate_text",
                "enum": ["generate_image", "edit_image", "create_video", "compose_music", "generate_text"]
            },
            "prompt": {
                "type": "string",
                "description": "Yaratıcı içerik için prompt/açıklama"
            },
            "style": {
                "type": "string",
                "description": "Stil: realistic, cartoon, abstract, vintage, modern",
                "enum": ["realistic", "cartoon", "abstract", "vintage", "modern"]
            },
            "size": {
                "type": "string",
                "description": "Boyut: 256x256, 512x512, 1024x1024",
                "enum": ["256x256", "512x512", "1024x1024"],
                "default": "512x512"
            },
            "count": {
                "type": "integer",
                "description": "Oluşturulacak içerik sayısı",
                "default": 1,
                "minimum": 1,
                "maximum": 4
            }
        },
        "required": ["action", "prompt"]
    }
    
    def __init__(self):
        super().__init__()
        # Instance variables
        object.__setattr__(self, 'openai_client', None)
        object.__setattr__(self, 'generated_content', [])
        self._initialize_clients()
    
    def _initialize_clients(self):
        """API istemcilerini başlat"""
        if not CREATIVE_AVAILABLE:
            return
        
        try:
            # OpenAI DALL-E
            if hasattr(config, 'openai_api_key') and config.openai_api_key:
                self.openai_client = openai.OpenAI(api_key=config.openai_api_key)
                logger.info("🎨 OpenAI DALL-E client initialized")
        except Exception as e:
            logger.error(f"❌ Creative content client initialization error: {e}")
    
    def generate_image(self, prompt: str, style: str = "realistic", 
                      size: str = "512x512", count: int = 1) -> Dict[str, Any]:
        """AI ile resim oluşturma"""
        if not CREATIVE_AVAILABLE:
            return {"error": "📦 Yaratıcı içerik kütüphaneleri yüklenmemiş"}
        
        if not self.openai_client:
            return {"error": "❌ OpenAI API anahtarı yapılandırılmamış"}
        
        try:
            # Stil ekleme
            style_prompts = {
                "realistic": "photorealistic, high quality, detailed",
                "cartoon": "cartoon style, animated, colorful",
                "abstract": "abstract art, artistic, creative",
                "vintage": "vintage style, retro, classic",
                "modern": "modern art, contemporary, sleek"
            }
            
            enhanced_prompt = f"{prompt}, {style_prompts.get(style, '')}"
            
            # DALL-E API çağrısı
            response = self.openai_client.images.generate(
                model="dall-e-3",
                prompt=enhanced_prompt,
                size=size,
                quality="standard",
                n=count
            )
            
            generated_images = []
            for i, image_data in enumerate(response.data):
                # Resmi indir
                image_url = image_data.url
                image_response = requests.get(image_url)
                
                # Dosya adı oluştur
                safe_prompt = "".join(c for c in prompt[:30] if c.isalnum() or c in (' ', '-', '_')).rstrip()
                filename = f"generated_image_{safe_prompt}_{i+1}.png"
                
                # Resmi kaydet
                with open(filename, 'wb') as f:
                    f.write(image_response.content)
                
                generated_images.append({
                    "filename": filename,
                    "url": image_url,
                    "revised_prompt": getattr(image_data, 'revised_prompt', prompt)
                })
            
            result = {
                "success": True,
                "prompt": prompt,
                "enhanced_prompt": enhanced_prompt,
                "style": style,
                "size": size,
                "count": count,
                "images": generated_images,
                "type": "image_generation"
            }
            
            self.generated_content.append(result)
            logger.info(f"🎨 {count} resim oluşturuldu: {prompt}")
            return result
            
        except Exception as e:
            error_msg = f"❌ Resim oluşturma hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    def edit_image(self, image_path: str, prompt: str, mask_path: Optional[str] = None) -> Dict[str, Any]:
        """Resim düzenleme"""
        if not CREATIVE_AVAILABLE:
            return {"error": "📦 Resim düzenleme kütüphaneleri yüklenmemiş"}
        
        try:
            if not os.path.exists(image_path):
                return {"error": f"❌ Resim dosyası bulunamadı: {image_path}"}
            
            # PIL ile resim yükle
            image = Image.open(image_path)
            
            # Basit düzenleme işlemleri
            if "resize" in prompt.lower():
                # Boyut değiştirme
                new_size = (512, 512)  # Varsayılan boyut
                image = image.resize(new_size, Image.Resampling.LANCZOS)
                
            elif "rotate" in prompt.lower():
                # Döndürme
                angle = 90  # Varsayılan açı
                image = image.rotate(angle, expand=True)
                
            elif "blur" in prompt.lower():
                # Bulanıklaştırma
                from PIL import ImageFilter
                image = image.filter(ImageFilter.BLUR)
                
            elif "sharpen" in prompt.lower():
                # Keskinleştirme
                from PIL import ImageFilter
                image = image.filter(ImageFilter.SHARPEN)
                
            elif "grayscale" in prompt.lower():
                # Gri tonlama
                image = image.convert('L')
                
            # Düzenlenmiş resmi kaydet
            base_name = Path(image_path).stem
            output_filename = f"{base_name}_edited.png"
            image.save(output_filename)
            
            result = {
                "success": True,
                "original_image": image_path,
                "edited_image": output_filename,
                "prompt": prompt,
                "operations": ["basic_editing"],
                "type": "image_editing"
            }
            
            self.generated_content.append(result)
            logger.info(f"✏️ Resim düzenlendi: {image_path}")
            return result
            
        except Exception as e:
            error_msg = f"❌ Resim düzenleme hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    def create_video(self, images: List[str], duration: float = 2.0, fps: int = 24) -> Dict[str, Any]:
        """Resimlerden video oluşturma"""
        if not CREATIVE_AVAILABLE:
            return {"error": "📦 Video kütüphaneleri yüklenmemiş"}
        
        try:
            if not images:
                return {"error": "❌ Video için resim listesi boş"}
            
            # İlk resmin boyutunu al
            first_image = cv2.imread(images[0])
            if first_image is None:
                return {"error": f"❌ İlk resim yüklenemedi: {images[0]}"}
            
            height, width, layers = first_image.shape
            
            # Video writer oluştur
            output_filename = "generated_video.mp4"
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            video_writer = cv2.VideoWriter(output_filename, fourcc, fps, (width, height))
            
            # Her resmi video'ya ekle
            frames_per_image = int(fps * duration)
            
            for image_path in images:
                if not os.path.exists(image_path):
                    logger.warning(f"⚠️ Resim bulunamadı, atlanıyor: {image_path}")
                    continue
                
                img = cv2.imread(image_path)
                if img is None:
                    continue
                
                # Boyutu ayarla
                img = cv2.resize(img, (width, height))
                
                # Aynı resmi birden fazla frame ekle
                for _ in range(frames_per_image):
                    video_writer.write(img)
            
            video_writer.release()
            
            result = {
                "success": True,
                "input_images": images,
                "output_video": output_filename,
                "duration_per_image": duration,
                "fps": fps,
                "total_frames": len(images) * frames_per_image,
                "type": "video_creation"
            }
            
            self.generated_content.append(result)
            logger.info(f"🎬 Video oluşturuldu: {len(images)} resimden")
            return result
            
        except Exception as e:
            error_msg = f"❌ Video oluşturma hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    def compose_music(self, prompt: str, style: str = "classical", duration: int = 30) -> Dict[str, Any]:
        """Basit müzik kompozisyonu"""
        try:
            # Basit MIDI benzeri müzik oluşturma simülasyonu
            notes = ["C", "D", "E", "F", "G", "A", "B"]
            scales = {
                "classical": [0, 2, 4, 5, 7, 9, 11],  # Major scale
                "blues": [0, 3, 5, 6, 7, 10],         # Blues scale
                "minor": [0, 2, 3, 5, 7, 8, 10],      # Natural minor
                "pentatonic": [0, 2, 4, 7, 9]         # Pentatonic
            }
            
            selected_scale = scales.get(style, scales["classical"])
            
            # Basit melodi oluştur
            import random
            melody = []
            for i in range(duration):  # Her saniye için bir nota
                note_index = random.choice(selected_scale)
                note = notes[note_index % len(notes)]
                octave = 4 + (note_index // len(notes))
                melody.append(f"{note}{octave}")
            
            # Müzik dosyası simülasyonu
            music_data = {
                "tempo": 120,
                "time_signature": "4/4",
                "key": "C major" if style == "classical" else f"{style} scale",
                "melody": melody,
                "duration": duration
            }
            
            # JSON olarak kaydet
            import json
            output_filename = f"composed_music_{style}.json"
            with open(output_filename, 'w') as f:
                json.dump(music_data, f, indent=2)
            
            result = {
                "success": True,
                "prompt": prompt,
                "style": style,
                "duration": duration,
                "output_file": output_filename,
                "music_data": music_data,
                "note": "Bu basit bir müzik kompozisyonu simülasyonudur. Gerçek ses dosyası için ek kütüphaneler gerekir.",
                "type": "music_composition"
            }
            
            self.generated_content.append(result)
            logger.info(f"🎵 Müzik kompozisyonu oluşturuldu: {style} tarzında")
            return result
            
        except Exception as e:
            error_msg = f"❌ Müzik kompozisyonu hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    def generate_text(self, prompt: str, style: str = "creative", length: int = 200) -> Dict[str, Any]:
        """Yaratıcı metin üretimi"""
        try:
            # Basit metin şablonları
            templates = {
                "creative": [
                    "Bir zamanlar {prompt} ile ilgili büyülü bir hikaye vardı...",
                    "Hayal gücünüzü kullanarak {prompt} hakkında düşünün...",
                    "Yaratıcılığın sınırlarını zorlayan {prompt} konsepti..."
                ],
                "technical": [
                    "{prompt} konusunda teknik bir analiz yapacak olursak...",
                    "Bu {prompt} sisteminin çalışma prensibi şu şekildedir...",
                    "Mühendislik açısından {prompt} değerlendirildiğinde..."
                ],
                "poetic": [
                    "Şiirsel bir dille {prompt} anlatımı...",
                    "Kelimelerle {prompt} resmi çizmek...",
                    "Duygusal derinlikle {prompt} keşfi..."
                ]
            }
            
            # Şablon seç
            style_templates = templates.get(style, templates["creative"])
            import random
            selected_template = random.choice(style_templates)
            
            # Metni oluştur
            generated_text = selected_template.format(prompt=prompt)
            
            # Uzunluğa göre genişlet
            if len(generated_text) < length:
                extensions = [
                    " Bu konu hakkında daha derinlemesine düşündüğümüzde...",
                    " Farklı perspektiflerden bakıldığında...",
                    " Yaratıcı yaklaşımlarla ele alındığında...",
                    " İnovatif çözümler arandığında...",
                    " Detaylı inceleme yapıldığında..."
                ]
                
                while len(generated_text) < length:
                    extension = random.choice(extensions)
                    generated_text += extension
                    if len(generated_text) >= length:
                        break
            
            # Uzunluğu sınırla
            if len(generated_text) > length:
                generated_text = generated_text[:length] + "..."
            
            # Dosyaya kaydet
            output_filename = f"generated_text_{style}.txt"
            with open(output_filename, 'w', encoding='utf-8') as f:
                f.write(generated_text)
            
            result = {
                "success": True,
                "prompt": prompt,
                "style": style,
                "length": len(generated_text),
                "generated_text": generated_text,
                "output_file": output_filename,
                "type": "text_generation"
            }
            
            self.generated_content.append(result)
            logger.info(f"📝 Yaratıcı metin oluşturuldu: {style} tarzında")
            return result
            
        except Exception as e:
            error_msg = f"❌ Metin üretimi hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    def get_content_history(self) -> Dict[str, Any]:
        """Oluşturulan içerik geçmişi"""
        return {
            "success": True,
            "total_content": len(self.generated_content),
            "content_types": list(set(item.get("type", "unknown") for item in self.generated_content)),
            "recent_content": self.generated_content[-5:] if self.generated_content else [],
            "type": "content_history"
        }
    
    async def execute(self, action: str, **kwargs) -> Dict[str, Any]:
        """Ana execution fonksiyonu"""
        try:
            if action == "generate_image":
                return self.generate_image(
                    kwargs.get('prompt', ''),
                    kwargs.get('style', 'realistic'),
                    kwargs.get('size', '512x512'),
                    kwargs.get('count', 1)
                )
            elif action == "edit_image":
                return self.edit_image(
                    kwargs.get('image_path', ''),
                    kwargs.get('prompt', ''),
                    kwargs.get('mask_path')
                )
            elif action == "create_video":
                return self.create_video(
                    kwargs.get('images', []),
                    kwargs.get('duration', 2.0),
                    kwargs.get('fps', 24)
                )
            elif action == "compose_music":
                return self.compose_music(
                    kwargs.get('prompt', ''),
                    kwargs.get('style', 'classical'),
                    kwargs.get('duration', 30)
                )
            elif action == "generate_text":
                return self.generate_text(
                    kwargs.get('prompt', ''),
                    kwargs.get('style', 'creative'),
                    kwargs.get('length', 200)
                )
            elif action == "history":
                return self.get_content_history()
            else:
                return {
                    "error": f"❌ Bilinmeyen action: {action}",
                    "available_actions": ["generate_image", "edit_image", "create_video", "compose_music", "generate_text", "history"]
                }
        except Exception as e:
            return {"error": f"❌ Execution hatası: {str(e)}"}


# Global instance will be created when needed
