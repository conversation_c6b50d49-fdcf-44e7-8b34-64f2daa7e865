#!/usr/bin/env python3
# coding: utf-8
"""
Matematik & Bilim Motoru - OpenManus Tool
Wolfram Alpha API, LaTeX desteği, bilim<PERSON> hesaplama, form<PERSON>l çözme, grafik çizme
"""

import re
import math
import cmath
import sympy as sp
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass

try:
    import numpy as np
    import matplotlib.pyplot as plt
    import scipy.optimize as opt
    import scipy.integrate as integrate
    import scipy.stats as stats
    import requests
    MATH_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Matematik kütüphaneleri yüklü değil: {e}")
    MATH_AVAILABLE = False

from app.logger import logger
from app.tool.base import BaseTool
from app.config import config


@dataclass
class MathResult:
    """Matematik işlem sonucu"""
    expression: str
    result: Any
    steps: List[str]
    latex: str
    plot_data: Optional[Dict] = None
    error: Optional[str] = None


class MathScienceTool(BaseTool):
    """Matematik ve bilim hesaplama aracı"""
    
    name: str = "math_science"
    description: str = "Matematik hesaplamaları, formül çözme, grafik çizme, bilimsel hesaplama yapar"
    parameters: dict = {
        "type": "object",
        "properties": {
            "action": {
                "type": "string",
                "description": "Yapılacak işlem: calculate, solve, plot, integrate, differentiate, matrix, statistics",
                "enum": ["calculate", "solve", "plot", "integrate", "differentiate", "matrix", "statistics"]
            },
            "expression": {
                "type": "string",
                "description": "Matematik ifadesi veya denklemi"
            },
            "variable": {
                "type": "string",
                "description": "Çözülecek değişken (varsayılan: x)",
                "default": "x"
            },
            "range_start": {
                "type": "number",
                "description": "Grafik için başlangıç değeri",
                "default": -10
            },
            "range_end": {
                "type": "number", 
                "description": "Grafik için bitiş değeri",
                "default": 10
            },
            "data": {
                "type": "array",
                "description": "İstatistik için veri listesi"
            }
        },
        "required": ["action", "expression"]
    }
    
    def __init__(self):
        super().__init__()
        # Instance variables
        object.__setattr__(self, 'wolfram_api_key', getattr(config, 'wolfram_api_key', None))
        object.__setattr__(self, 'calculation_history', [])
    
    def calculate(self, expression: str) -> Dict[str, Any]:
        """Basit matematik hesaplama"""
        if not MATH_AVAILABLE:
            return {"error": "📦 Matematik kütüphaneleri yüklenmemiş"}
        
        try:
            # Güvenli matematik ifadesi değerlendirme
            safe_dict = {
                '__builtins__': {},
                'abs': abs, 'round': round, 'min': min, 'max': max,
                'sum': sum, 'len': len, 'pow': pow,
                'sin': math.sin, 'cos': math.cos, 'tan': math.tan,
                'asin': math.asin, 'acos': math.acos, 'atan': math.atan,
                'sinh': math.sinh, 'cosh': math.cosh, 'tanh': math.tanh,
                'log': math.log, 'log10': math.log10, 'exp': math.exp,
                'sqrt': math.sqrt, 'pi': math.pi, 'e': math.e,
                'factorial': math.factorial, 'gcd': math.gcd,
                'degrees': math.degrees, 'radians': math.radians
            }
            
            # SymPy ile sembolik hesaplama
            try:
                sympy_expr = sp.sympify(expression)
                sympy_result = sympy_expr.evalf()
                latex_expr = sp.latex(sympy_expr)
                
                # Adım adım çözüm
                steps = [f"İfade: {expression}"]
                if sympy_expr != sympy_result:
                    steps.append(f"Hesaplama: {sympy_result}")
                
                result = {
                    "success": True,
                    "expression": expression,
                    "result": str(sympy_result),
                    "numeric_result": float(sympy_result) if sympy_result.is_real else complex(sympy_result),
                    "latex": latex_expr,
                    "steps": steps,
                    "type": "calculation"
                }
                
            except:
                # Fallback: Python eval
                result_value = eval(expression, safe_dict)
                result = {
                    "success": True,
                    "expression": expression,
                    "result": str(result_value),
                    "numeric_result": result_value,
                    "latex": expression,
                    "steps": [f"İfade: {expression}", f"Sonuç: {result_value}"],
                    "type": "calculation"
                }
            
            self.calculation_history.append(result)
            logger.info(f"🧮 Hesaplama tamamlandı: {expression} = {result['result']}")
            return result
            
        except Exception as e:
            error_msg = f"❌ Hesaplama hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    def solve_equation(self, equation: str, variable: str = "x") -> Dict[str, Any]:
        """Denklem çözme"""
        if not MATH_AVAILABLE:
            return {"error": "📦 Matematik kütüphaneleri yüklenmemiş"}
        
        try:
            # SymPy ile denklem çözme
            var = sp.Symbol(variable)
            
            # Eşitlik işaretini kontrol et
            if '=' in equation:
                left, right = equation.split('=', 1)
                eq = sp.Eq(sp.sympify(left.strip()), sp.sympify(right.strip()))
            else:
                # Eşitlik yoksa sıfıra eşit kabul et
                eq = sp.Eq(sp.sympify(equation), 0)
            
            # Çözümleri bul
            solutions = sp.solve(eq, var)
            
            # LaTeX formatı
            latex_eq = sp.latex(eq)
            
            # Adım adım çözüm
            steps = [
                f"Denklem: {equation}",
                f"Değişken: {variable}",
                f"SymPy formatı: {eq}"
            ]
            
            if solutions:
                steps.append(f"Çözümler bulundu: {len(solutions)} adet")
                for i, sol in enumerate(solutions, 1):
                    steps.append(f"Çözüm {i}: {variable} = {sol}")
            else:
                steps.append("Gerçek çözüm bulunamadı")
            
            result = {
                "success": True,
                "equation": equation,
                "variable": variable,
                "solutions": [str(sol) for sol in solutions],
                "numeric_solutions": [complex(sol.evalf()) for sol in solutions if sol.is_finite],
                "latex": latex_eq,
                "steps": steps,
                "type": "equation_solving"
            }
            
            self.calculation_history.append(result)
            logger.info(f"🔍 Denklem çözüldü: {equation}, {len(solutions)} çözüm")
            return result
            
        except Exception as e:
            error_msg = f"❌ Denklem çözme hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    def plot_function(self, expression: str, variable: str = "x", 
                     range_start: float = -10, range_end: float = 10) -> Dict[str, Any]:
        """Fonksiyon grafiği çizme"""
        if not MATH_AVAILABLE:
            return {"error": "📦 Grafik kütüphaneleri yüklenmemiş"}
        
        try:
            # SymPy ile fonksiyon tanımla
            var = sp.Symbol(variable)
            func = sp.sympify(expression)
            
            # Numpy array oluştur
            x_vals = np.linspace(range_start, range_end, 1000)
            
            # Fonksiyonu numpy fonksiyonuna çevir
            func_lambdified = sp.lambdify(var, func, 'numpy')
            
            try:
                y_vals = func_lambdified(x_vals)
            except:
                # Karmaşık sayılar için
                y_vals = [complex(func.subs(var, x_val).evalf()) for x_val in x_vals]
                y_vals = np.array([y.real if abs(y.imag) < 1e-10 else np.nan for y in y_vals])
            
            # Matplotlib ile grafik çiz
            plt.figure(figsize=(10, 6))
            plt.plot(x_vals, y_vals, 'b-', linewidth=2, label=f'f({variable}) = {expression}')
            plt.grid(True, alpha=0.3)
            plt.xlabel(variable)
            plt.ylabel(f'f({variable})')
            plt.title(f'Grafik: {expression}')
            plt.legend()
            
            # Eksenleri çiz
            plt.axhline(y=0, color='k', linewidth=0.5)
            plt.axvline(x=0, color='k', linewidth=0.5)
            
            # Grafiği kaydet
            plot_filename = f"plot_{expression.replace('/', '_div_').replace('*', '_mul_').replace('+', '_plus_').replace('-', '_minus_')}.png"
            plt.savefig(plot_filename, dpi=150, bbox_inches='tight')
            plt.close()
            
            # LaTeX formatı
            latex_expr = sp.latex(func)
            
            result = {
                "success": True,
                "expression": expression,
                "variable": variable,
                "range": [range_start, range_end],
                "plot_file": plot_filename,
                "latex": latex_expr,
                "data_points": len(x_vals),
                "type": "function_plot"
            }
            
            self.calculation_history.append(result)
            logger.info(f"📈 Grafik oluşturuldu: {expression}")
            return result
            
        except Exception as e:
            error_msg = f"❌ Grafik çizme hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    def integrate_function(self, expression: str, variable: str = "x", 
                          lower_limit: Optional[float] = None, 
                          upper_limit: Optional[float] = None) -> Dict[str, Any]:
        """İntegral hesaplama"""
        if not MATH_AVAILABLE:
            return {"error": "📦 Matematik kütüphaneleri yüklenmemiş"}
        
        try:
            var = sp.Symbol(variable)
            func = sp.sympify(expression)
            
            steps = [f"Fonksiyon: {expression}"]
            
            if lower_limit is not None and upper_limit is not None:
                # Belirli integral
                integral_result = sp.integrate(func, (var, lower_limit, upper_limit))
                steps.append(f"Belirli integral: ∫[{lower_limit} to {upper_limit}] {expression} d{variable}")
                integral_type = "definite"
            else:
                # Belirsiz integral
                integral_result = sp.integrate(func, var)
                steps.append(f"Belirsiz integral: ∫ {expression} d{variable}")
                integral_type = "indefinite"
            
            steps.append(f"Sonuç: {integral_result}")
            
            # LaTeX formatı
            latex_func = sp.latex(func)
            latex_result = sp.latex(integral_result)
            
            result = {
                "success": True,
                "expression": expression,
                "variable": variable,
                "integral_result": str(integral_result),
                "numeric_result": float(integral_result.evalf()) if integral_result.is_real else complex(integral_result.evalf()),
                "latex_function": latex_func,
                "latex_result": latex_result,
                "steps": steps,
                "type": f"{integral_type}_integral"
            }
            
            if lower_limit is not None and upper_limit is not None:
                result["limits"] = [lower_limit, upper_limit]
            
            self.calculation_history.append(result)
            logger.info(f"∫ İntegral hesaplandı: {expression}")
            return result
            
        except Exception as e:
            error_msg = f"❌ İntegral hesaplama hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    def differentiate_function(self, expression: str, variable: str = "x", 
                              order: int = 1) -> Dict[str, Any]:
        """Türev hesaplama"""
        if not MATH_AVAILABLE:
            return {"error": "📦 Matematik kütüphaneleri yüklenmemiş"}
        
        try:
            var = sp.Symbol(variable)
            func = sp.sympify(expression)
            
            steps = [f"Fonksiyon: {expression}"]
            
            # Türev hesapla
            derivative = func
            for i in range(order):
                derivative = sp.diff(derivative, var)
                steps.append(f"{i+1}. türev: {derivative}")
            
            # LaTeX formatı
            latex_func = sp.latex(func)
            latex_derivative = sp.latex(derivative)
            
            result = {
                "success": True,
                "expression": expression,
                "variable": variable,
                "order": order,
                "derivative": str(derivative),
                "latex_function": latex_func,
                "latex_derivative": latex_derivative,
                "steps": steps,
                "type": "differentiation"
            }
            
            self.calculation_history.append(result)
            logger.info(f"d/d{variable} Türev hesaplandı: {expression}")
            return result
            
        except Exception as e:
            error_msg = f"❌ Türev hesaplama hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    def matrix_operations(self, operation: str, matrix_a: List[List], 
                         matrix_b: Optional[List[List]] = None) -> Dict[str, Any]:
        """Matris işlemleri"""
        if not MATH_AVAILABLE:
            return {"error": "📦 Matematik kütüphaneleri yüklenmemiş"}
        
        try:
            # NumPy matrisleri oluştur
            A = np.array(matrix_a)
            
            steps = [f"Matris A: {A.shape}"]
            
            if operation == "determinant":
                if A.shape[0] != A.shape[1]:
                    return {"error": "❌ Determinant için kare matris gerekli"}
                result_value = np.linalg.det(A)
                steps.append(f"Determinant hesaplandı: {result_value}")
                
            elif operation == "inverse":
                if A.shape[0] != A.shape[1]:
                    return {"error": "❌ Ters matris için kare matris gerekli"}
                result_value = np.linalg.inv(A)
                steps.append("Ters matris hesaplandı")
                
            elif operation == "eigenvalues":
                if A.shape[0] != A.shape[1]:
                    return {"error": "❌ Özdeğerler için kare matris gerekli"}
                eigenvals, eigenvecs = np.linalg.eig(A)
                result_value = {"eigenvalues": eigenvals.tolist(), "eigenvectors": eigenvecs.tolist()}
                steps.append(f"Özdeğerler hesaplandı: {len(eigenvals)} adet")
                
            elif operation == "multiply":
                if matrix_b is None:
                    return {"error": "❌ Çarpma için ikinci matris gerekli"}
                B = np.array(matrix_b)
                if A.shape[1] != B.shape[0]:
                    return {"error": f"❌ Matris boyutları uyumsuz: {A.shape} × {B.shape}"}
                result_value = np.dot(A, B)
                steps.append(f"Matris çarpımı: {A.shape} × {B.shape} = {result_value.shape}")
                
            elif operation == "add":
                if matrix_b is None:
                    return {"error": "❌ Toplama için ikinci matris gerekli"}
                B = np.array(matrix_b)
                if A.shape != B.shape:
                    return {"error": f"❌ Matris boyutları uyumsuz: {A.shape} ≠ {B.shape}"}
                result_value = A + B
                steps.append("Matris toplamı hesaplandı")
                
            else:
                return {"error": f"❌ Desteklenmeyen işlem: {operation}"}
            
            # Sonucu formatla
            if isinstance(result_value, np.ndarray):
                result_formatted = result_value.tolist()
            else:
                result_formatted = result_value
            
            result = {
                "success": True,
                "operation": operation,
                "matrix_a": matrix_a,
                "result": result_formatted,
                "steps": steps,
                "type": "matrix_operation"
            }
            
            if matrix_b is not None:
                result["matrix_b"] = matrix_b
            
            self.calculation_history.append(result)
            logger.info(f"🔢 Matris işlemi tamamlandı: {operation}")
            return result
            
        except Exception as e:
            error_msg = f"❌ Matris işlemi hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    def statistics_analysis(self, data: List[float]) -> Dict[str, Any]:
        """İstatistiksel analiz"""
        if not MATH_AVAILABLE:
            return {"error": "📦 İstatistik kütüphaneleri yüklenmemiş"}
        
        try:
            data_array = np.array(data)
            
            # Temel istatistikler
            mean_val = np.mean(data_array)
            median_val = np.median(data_array)
            std_val = np.std(data_array)
            var_val = np.var(data_array)
            min_val = np.min(data_array)
            max_val = np.max(data_array)
            
            # Çeyrekler
            q1 = np.percentile(data_array, 25)
            q3 = np.percentile(data_array, 75)
            iqr = q3 - q1
            
            # Normallik testi (Shapiro-Wilk)
            if len(data) >= 3:
                shapiro_stat, shapiro_p = stats.shapiro(data_array)
                is_normal = shapiro_p > 0.05
            else:
                shapiro_stat, shapiro_p = None, None
                is_normal = None
            
            steps = [
                f"Veri sayısı: {len(data)}",
                f"Ortalama: {mean_val:.4f}",
                f"Medyan: {median_val:.4f}",
                f"Standart sapma: {std_val:.4f}",
                f"Varyans: {var_val:.4f}",
                f"Min-Max: {min_val:.4f} - {max_val:.4f}",
                f"IQR: {iqr:.4f}"
            ]
            
            if shapiro_p is not None:
                steps.append(f"Normallik testi p-değeri: {shapiro_p:.4f}")
                steps.append(f"Normal dağılım: {'Evet' if is_normal else 'Hayır'}")
            
            result = {
                "success": True,
                "data_count": len(data),
                "mean": mean_val,
                "median": median_val,
                "std": std_val,
                "variance": var_val,
                "min": min_val,
                "max": max_val,
                "q1": q1,
                "q3": q3,
                "iqr": iqr,
                "range": max_val - min_val,
                "steps": steps,
                "type": "statistics"
            }
            
            if shapiro_p is not None:
                result["normality_test"] = {
                    "statistic": shapiro_stat,
                    "p_value": shapiro_p,
                    "is_normal": is_normal
                }
            
            self.calculation_history.append(result)
            logger.info(f"📊 İstatistik analizi tamamlandı: {len(data)} veri")
            return result
            
        except Exception as e:
            error_msg = f"❌ İstatistik analizi hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}
    
    async def execute(self, action: str, **kwargs) -> Dict[str, Any]:
        """Ana execution fonksiyonu"""
        try:
            if action == "calculate":
                return self.calculate(kwargs.get('expression', ''))
            elif action == "solve":
                return self.solve_equation(
                    kwargs.get('expression', ''),
                    kwargs.get('variable', 'x')
                )
            elif action == "plot":
                return self.plot_function(
                    kwargs.get('expression', ''),
                    kwargs.get('variable', 'x'),
                    kwargs.get('range_start', -10),
                    kwargs.get('range_end', 10)
                )
            elif action == "integrate":
                return self.integrate_function(
                    kwargs.get('expression', ''),
                    kwargs.get('variable', 'x'),
                    kwargs.get('lower_limit'),
                    kwargs.get('upper_limit')
                )
            elif action == "differentiate":
                return self.differentiate_function(
                    kwargs.get('expression', ''),
                    kwargs.get('variable', 'x'),
                    kwargs.get('order', 1)
                )
            elif action == "matrix":
                return self.matrix_operations(
                    kwargs.get('operation', 'determinant'),
                    kwargs.get('matrix_a', []),
                    kwargs.get('matrix_b')
                )
            elif action == "statistics":
                return self.statistics_analysis(kwargs.get('data', []))
            else:
                return {
                    "error": f"❌ Bilinmeyen action: {action}",
                    "available_actions": ["calculate", "solve", "plot", "integrate", "differentiate", "matrix", "statistics"]
                }
        except Exception as e:
            return {"error": f"❌ Execution hatası: {str(e)}"}


# Global instance will be created when needed
