#!/usr/bin/env python3
# coding: utf-8
"""
Gelişmiş Veri Analizi Araçları - OpenManus Tool
Excel, CSV, JSON dosyalarını işleme, grafik oluşturma ve rapor üretme
"""

import base64
import json
import os
from io import BytesIO
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

try:
    import matplotlib.pyplot as plt
    import numpy as np
    import openpyxl
    import pandas as pd
    import plotly.express as px
    import plotly.graph_objects as go
    import seaborn as sns
    from plotly.offline import plot
    from scipy import stats
    from sklearn.cluster import KMeans
    from sklearn.linear_model import LinearRegression
    from sklearn.preprocessing import StandardScaler

    ANALYSIS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Veri analizi kütüphaneleri yüklü değil: {e}")
    ANALYSIS_AVAILABLE = False

from app.logger import logger
from app.tool.base import BaseTool


class DataAnalysisTool(BaseTool):
    """Gelişmiş veri analizi aracı"""

    name: str = "data_analysis"
    description: str = (
        "Excel, CSV, JSON dosyalarını analiz eder, grafikler oluşturur ve raporlar üretir"
    )
    parameters: dict = {
        "type": "object",
        "properties": {
            "action": {
                "type": "string",
                "description": "Yapılacak işlem: load_file, analyze, create_chart, generate_report",
                "enum": ["load_file", "analyze", "create_chart", "generate_report"],
            },
            "file_path": {
                "type": "string",
                "description": "Analiz edilecek dosya yolu (Excel, CSV, JSON)",
            },
            "chart_type": {
                "type": "string",
                "description": "Grafik türü: histogram, scatter, line, bar, box, correlation_heatmap",
                "enum": [
                    "histogram",
                    "scatter",
                    "line",
                    "bar",
                    "box",
                    "correlation_heatmap",
                ],
            },
            "x_column": {"type": "string", "description": "X ekseni için sütun adı"},
            "y_column": {
                "type": "string",
                "description": "Y ekseni için sütun adı (opsiyonel)",
            },
            "title": {"type": "string", "description": "Grafik başlığı (opsiyonel)"},
            "format": {
                "type": "string",
                "description": "Rapor formatı (şu anda sadece html)",
                "enum": ["html"],
            },
        },
        "required": ["action"],
    }

    def __init__(self):
        super().__init__()
        # Instance variables for data analysis
        object.__setattr__(self, "data", None)
        object.__setattr__(self, "file_path", None)
        object.__setattr__(self, "analysis_results", {})

    def load_file(self, file_path: str) -> Dict[str, Any]:
        """Dosya yükleme - Excel, CSV, JSON destekli"""
        if not ANALYSIS_AVAILABLE:
            return {
                "error": "📦 Veri analizi kütüphaneleri yüklenmemiş. pip install pandas matplotlib seaborn plotly scikit-learn openpyxl"
            }

        try:
            file_path = Path(file_path)
            self.file_path = file_path

            if not file_path.exists():
                return {"error": f"❌ Dosya bulunamadı: {file_path}"}

            # Dosya türüne göre yükleme
            if file_path.suffix.lower() in [".xlsx", ".xls"]:
                self.data = pd.read_excel(file_path)
            elif file_path.suffix.lower() == ".csv":
                # Encoding otomatik tespiti
                try:
                    self.data = pd.read_csv(file_path, encoding="utf-8")
                except UnicodeDecodeError:
                    self.data = pd.read_csv(file_path, encoding="latin-1")
            elif file_path.suffix.lower() == ".json":
                with open(file_path, "r", encoding="utf-8") as f:
                    json_data = json.load(f)
                self.data = pd.json_normalize(json_data)
            else:
                return {"error": f"❌ Desteklenmeyen dosya türü: {file_path.suffix}"}

            # Temel bilgiler
            info = {
                "success": True,
                "message": f"✅ Dosya başarıyla yüklendi: {file_path.name}",
                "file_name": file_path.name,
                "shape": self.data.shape,
                "columns": list(self.data.columns),
                "data_types": self.data.dtypes.astype(str).to_dict(),
                "memory_usage": f"{self.data.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB",
                "preview": self.data.head().to_dict("records"),
            }

            logger.info(f"📊 Dosya başarıyla yüklendi: {file_path.name}")
            return info

        except Exception as e:
            error_msg = f"❌ Dosya yükleme hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}

    def basic_analysis(self) -> Dict[str, Any]:
        """Temel istatistiksel analiz"""
        if self.data is None:
            return {"error": "❌ Önce bir dosya yükleyin: load_file('dosya_yolu')"}

        try:
            analysis = {
                "success": True,
                "shape": self.data.shape,
                "missing_values": self.data.isnull().sum().to_dict(),
                "numeric_summary": {},
                "categorical_summary": {},
                "correlations": {},
            }

            # Sayısal sütunlar için özet
            numeric_cols = self.data.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0:
                analysis["numeric_summary"] = (
                    self.data[numeric_cols].describe().to_dict()
                )

            # Kategorik sütunlar için özet
            categorical_cols = self.data.select_dtypes(include=["object"]).columns
            for col in categorical_cols:
                analysis["categorical_summary"][col] = {
                    "unique_count": self.data[col].nunique(),
                    "top_values": self.data[col].value_counts().head().to_dict(),
                }

            # Sayısal sütunlar için korelasyon
            if len(numeric_cols) > 1:
                analysis["correlations"] = self.data[numeric_cols].corr().to_dict()

            self.analysis_results["basic"] = analysis
            logger.info("📈 Temel analiz tamamlandı")
            return analysis

        except Exception as e:
            error_msg = f"❌ Temel analiz hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}

    def create_visualization(
        self, chart_type: str, x_column: str, y_column: str = None, title: str = None
    ) -> Dict[str, Any]:
        """Grafik oluşturma"""
        if self.data is None:
            return {"error": "❌ Önce bir dosya yükleyin: load_file('dosya_yolu')"}

        if not ANALYSIS_AVAILABLE:
            return {"error": "📦 Grafik kütüphaneleri yüklenmemiş"}

        try:
            # Grafik türüne göre oluşturma
            if chart_type == "histogram":
                fig = px.histogram(
                    self.data, x=x_column, title=title or f"📊 {x_column} Histogram"
                )
            elif chart_type == "scatter":
                if not y_column:
                    return {"error": "❌ Scatter plot için y_column gerekli"}
                fig = px.scatter(
                    self.data,
                    x=x_column,
                    y=y_column,
                    title=title or f"📈 {x_column} vs {y_column}",
                )
            elif chart_type == "line":
                fig = px.line(
                    self.data,
                    x=x_column,
                    y=y_column,
                    title=title or f"📉 {x_column} - {y_column} Line Chart",
                )
            elif chart_type == "bar":
                value_counts = self.data[x_column].value_counts()
                fig = px.bar(
                    x=value_counts.index,
                    y=value_counts.values,
                    title=title or f"📊 {x_column} Bar Chart",
                )
            elif chart_type == "box":
                fig = px.box(
                    self.data, y=x_column, title=title or f"📦 {x_column} Box Plot"
                )
            elif chart_type == "correlation_heatmap":
                numeric_data = self.data.select_dtypes(include=[np.number])
                if len(numeric_data.columns) < 2:
                    return {"error": "❌ Korelasyon için en az 2 sayısal sütun gerekli"}
                corr_matrix = numeric_data.corr()
                fig = px.imshow(
                    corr_matrix,
                    text_auto=True,
                    aspect="auto",
                    title=title or "🔥 Correlation Heatmap",
                )
            else:
                return {
                    "error": f"❌ Desteklenmeyen grafik türü: {chart_type}. Desteklenenler: histogram, scatter, line, bar, box, correlation_heatmap"
                }

            # HTML olarak kaydet
            chart_html = fig.to_html(include_plotlyjs="cdn")

            # Grafik dosyasını kaydet
            chart_filename = f"chart_{chart_type}_{x_column}.html"
            with open(chart_filename, "w", encoding="utf-8") as f:
                f.write(chart_html)

            result = {
                "success": True,
                "message": f"✅ {chart_type} grafiği oluşturuldu",
                "chart_type": chart_type,
                "chart_file": chart_filename,
                "columns_used": [x_column] + ([y_column] if y_column else []),
                "html_preview": (
                    chart_html[:500] + "..." if len(chart_html) > 500 else chart_html
                ),
            }

            logger.info(f"📊 Grafik oluşturuldu: {chart_type}")
            return result

        except Exception as e:
            error_msg = f"❌ Grafik oluşturma hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}

    def generate_report(self, output_format: str = "html") -> Dict[str, Any]:
        """Kapsamlı rapor oluşturma"""
        if self.data is None:
            return {"error": "❌ Önce bir dosya yükleyin: load_file('dosya_yolu')"}

        try:
            # Temel analiz yap
            basic_stats = self.basic_analysis()

            if output_format == "html":
                html_report = self._generate_html_report(basic_stats)

                # Raporu dosyaya kaydet
                report_filename = f"data_analysis_report_{self.file_path.stem if self.file_path else 'unknown'}.html"
                with open(report_filename, "w", encoding="utf-8") as f:
                    f.write(html_report)

                return {
                    "success": True,
                    "message": f"✅ HTML raporu oluşturuldu: {report_filename}",
                    "format": "html",
                    "report_file": report_filename,
                    "preview": (
                        html_report[:1000] + "..."
                        if len(html_report) > 1000
                        else html_report
                    ),
                }
            else:
                return {
                    "error": f"❌ Desteklenmeyen format: {output_format}. Desteklenen: html"
                }

        except Exception as e:
            error_msg = f"❌ Rapor oluşturma hatası: {str(e)}"
            logger.error(error_msg)
            return {"error": error_msg}

    def _generate_html_report(self, basic_stats: Dict) -> str:
        """HTML raporu oluştur"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>📊 Veri Analizi Raporu</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }}
                .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; }}
                .section {{ margin: 30px 0; padding: 20px; border: 1px solid #e0e0e0; border-radius: 8px; }}
                .section h2 {{ color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px; }}
                table {{ border-collapse: collapse; width: 100%; margin: 15px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
                th {{ background-color: #667eea; color: white; }}
                .stat-box {{ display: inline-block; background: #f8f9fa; padding: 15px; margin: 10px; border-radius: 8px; border-left: 4px solid #667eea; }}
                .success {{ color: #28a745; }}
                .warning {{ color: #ffc107; }}
                .error {{ color: #dc3545; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>📊 OpenManus Veri Analizi Raporu</h1>
                    <p>📁 Dosya: {self.file_path.name if self.file_path else 'Bilinmiyor'}</p>
                    <p>🕒 Oluşturulma: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>

                <div class="section">
                    <h2>📈 Genel Bakış</h2>
                    <div class="stat-box">
                        <strong>📏 Boyut:</strong> {basic_stats.get('shape', [0, 0])[0]} satır × {basic_stats.get('shape', [0, 0])[1]} sütun
                    </div>
                    <div class="stat-box">
                        <strong>❌ Eksik Değer:</strong> {sum(basic_stats.get('missing_values', {}).values())} toplam
                    </div>
                    <div class="stat-box">
                        <strong>💾 Bellek:</strong> {self.data.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB
                    </div>
                </div>

                <div class="section">
                    <h2>📋 Sütun Detayları</h2>
                    <table>
                        <tr><th>Sütun Adı</th><th>Veri Tipi</th><th>Eksik Değer</th><th>Durum</th></tr>
        """

        for col in basic_stats.get("missing_values", {}):
            missing = basic_stats["missing_values"][col]
            dtype = str(self.data[col].dtype)
            status_class = "error" if missing > 0 else "success"
            status_text = f"❌ {missing} eksik" if missing > 0 else "✅ Temiz"
            html += f'<tr><td>{col}</td><td>{dtype}</td><td>{missing}</td><td class="{status_class}">{status_text}</td></tr>'

        html += """
                    </table>
                </div>

                <div class="section">
                    <h2>🔢 Sayısal Sütun İstatistikleri</h2>
        """

        numeric_summary = basic_stats.get("numeric_summary", {})
        if numeric_summary:
            html += "<table><tr><th>Sütun</th><th>Ortalama</th><th>Std</th><th>Min</th><th>Max</th></tr>"
            for col, stats in numeric_summary.items():
                if isinstance(stats, dict) and "mean" in stats:
                    html += f"""<tr>
                        <td>{col}</td>
                        <td>{stats['mean']:.2f}</td>
                        <td>{stats['std']:.2f}</td>
                        <td>{stats['min']:.2f}</td>
                        <td>{stats['max']:.2f}</td>
                    </tr>"""
            html += "</table>"
        else:
            html += "<p>📊 Sayısal sütun bulunamadı.</p>"

        html += """
                </div>

                <div class="section">
                    <h2>📝 Kategorik Sütun İstatistikleri</h2>
        """

        categorical_summary = basic_stats.get("categorical_summary", {})
        if categorical_summary:
            html += "<table><tr><th>Sütun</th><th>Benzersiz Değer</th><th>En Sık Değer</th></tr>"
            for col, stats in categorical_summary.items():
                top_values = list(stats["top_values"].keys())
                top_value = top_values[0] if top_values else "N/A"
                html += f"""<tr>
                    <td>{col}</td>
                    <td>{stats['unique_count']}</td>
                    <td>{top_value}</td>
                </tr>"""
            html += "</table>"
        else:
            html += "<p>📊 Kategorik sütun bulunamadı.</p>"

        html += """
                </div>

                <div class="section">
                    <h2>🎯 Öneriler</h2>
                    <ul>
                        <li>📊 Grafikler oluşturmak için: <code>create_visualization()</code> fonksiyonunu kullanın</li>
                        <li>🔍 Detaylı analiz için: Eksik değerleri temizleyin</li>
                        <li>📈 Trend analizi için: Zaman serisi sütunlarını kontrol edin</li>
                        <li>🎨 Görselleştirme için: Plotly grafikleri oluşturun</li>
                    </ul>
                </div>

                <div class="section">
                    <h2>🔧 OpenManus Veri Analizi Araçları</h2>
                    <p>Bu rapor OpenManus'un gelişmiş veri analizi araçları ile oluşturulmuştur.</p>
                    <p>🌟 Daha fazla özellik için: Multi-model AI, görsel editör, ve otomatik insight'lar yakında!</p>
                </div>
            </div>
        </body>
        </html>
        """

        return html

    def execute(self, action: str, **kwargs) -> Dict[str, Any]:
        """Ana execution fonksiyonu"""
        try:
            if action == "load_file":
                return self.load_file(kwargs.get("file_path", ""))
            elif action == "analyze":
                return self.basic_analysis()
            elif action == "create_chart":
                return self.create_visualization(
                    kwargs.get("chart_type", "histogram"),
                    kwargs.get("x_column", ""),
                    kwargs.get("y_column"),
                    kwargs.get("title"),
                )
            elif action == "generate_report":
                return self.generate_report(kwargs.get("format", "html"))
            else:
                return {
                    "error": f"❌ Bilinmeyen action: {action}",
                    "available_actions": [
                        "load_file",
                        "analyze",
                        "create_chart",
                        "generate_report",
                    ],
                }
        except Exception as e:
            return {"error": f"❌ Execution hatası: {str(e)}"}


# Global instance will be created when needed
