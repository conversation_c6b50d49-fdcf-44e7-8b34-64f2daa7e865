#!/usr/bin/env python3
# coding: utf-8
"""
OpenManus Web Interface with Embedded Browser
Manus AI'nin web sitesi içinde gömülü tarayıcı ile çalışması
"""

import asyncio
from datetime import datetime
from typing import List

import requests
import uvicorn
from fastapi import FastAPI, Form, Query, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse, JSONResponse, Response
from fastapi.staticfiles import StaticFiles

from app.agent.manus import Manus
from app.logger import logger

app = FastAPI(title="OpenManus SuperAI - Embedded Browser")

# Global değişkenler
current_agent = None
chat_history = []
connected_clients: List[WebSocket] = []


async def broadcast_to_clients(message: str):
    """Tüm bağlı istemcilere mesaj gönder"""
    for client in connected_clients[:]:  # Copy to avoid modification during iteration
        try:
            await client.send_text(message)
        except:
            connected_clients.remove(client)


def get_agent():
    """Agent instance'ını al veya oluştur"""
    global current_agent
    if current_agent is None:
        try:
            current_agent = Manus()

            # Embedded browser tool'una WebSocket callback ekle
            for tool in current_agent.available_tools.tools:
                if hasattr(tool, "websocket_callback"):
                    object.__setattr__(tool, "websocket_callback", broadcast_to_clients)

            logger.info("Agent başarıyla oluşturuldu")
        except Exception as e:
            logger.error(f"Agent oluşturulurken hata: {e}")
            current_agent = None
    return current_agent


@app.get("/", response_class=HTMLResponse)
async def home():
    """Ana sayfa - Embedded Browser ile"""
    html_content = """
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenManus SuperAI - Embedded Browser</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Inter', 'Segoe UI', 'Roboto', sans-serif;
            background: #0a0a0a;
            height: 100vh;
            overflow: hidden;
            color: #ffffff;
        }
        .main-container {
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #0f0f0f 100%);
            border: 1px solid #333;
            width: 100%;
            height: 100vh;
            display: flex;
            overflow: hidden;
            position: relative;
        }
        .main-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        /* Sol Panel - Chat */
        .chat-panel {
            width: 40%;
            display: flex;
            flex-direction: column;
            border-right: 1px solid #333;
            background: rgba(15, 15, 15, 0.8);
            backdrop-filter: blur(10px);
            position: relative;
            z-index: 1;
        }
        .chat-header {
            background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
            border-bottom: 1px solid #333;
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }
        .chat-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(120, 119, 198, 0.1) 0%, rgba(255, 119, 198, 0.1) 100%);
            pointer-events: none;
        }
        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 8px;
            font-weight: 600;
            background: linear-gradient(135deg, #fff 0%, #a0a0a0 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            z-index: 1;
        }
        .chat-header p {
            opacity: 0.8;
            font-size: 14px;
            color: #a0a0a0;
            position: relative;
            z-index: 1;
        }
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: rgba(10, 10, 10, 0.5);
            backdrop-filter: blur(10px);
        }
        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }
        .chat-messages::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }
        .chat-messages::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }
        .message {
            margin-bottom: 16px;
            display: flex;
            align-items: flex-start;
        }
        .message.user { justify-content: flex-end; }
        .message-content {
            max-width: 85%;
            padding: 16px 20px;
            border-radius: 20px;
            word-wrap: break-word;
            font-size: 14px;
            line-height: 1.5;
            position: relative;
            backdrop-filter: blur(10px);
        }
        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        .message.agent .message-content {
            background: rgba(30, 30, 30, 0.8);
            border: 1px solid #333;
            color: #e0e0e0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }
        .timestamp {
            font-size: 11px;
            opacity: 0.6;
            margin-top: 6px;
            color: #888;
        }
        .input-container {
            padding: 20px;
            background: rgba(20, 20, 20, 0.8);
            border-top: 1px solid #333;
            backdrop-filter: blur(10px);
        }
        .input-form {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        .message-input {
            flex: 1;
            padding: 16px 20px;
            background: rgba(40, 40, 40, 0.8);
            border: 1px solid #444;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
            color: white;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        .message-input::placeholder {
            color: #888;
        }
        .message-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
        }
        .send-button {
            padding: 16px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        .send-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        /* Sağ Panel - Browser */
        .browser-panel {
            width: 60%;
            display: flex;
            flex-direction: column;
            background: rgba(15, 15, 15, 0.8);
            backdrop-filter: blur(10px);
            position: relative;
            z-index: 1;
        }
        .browser-header {
            background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
            border-bottom: 1px solid #333;
            color: white;
            padding: 16px 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 14px;
        }
        .browser-controls {
            display: flex;
            gap: 8px;
        }
        .browser-btn {
            background: rgba(60, 60, 60, 0.8);
            border: 1px solid #444;
            color: white;
            padding: 10px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        .browser-btn:hover {
            background: rgba(80, 80, 80, 0.8);
            transform: translateY(-1px);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
        }
        .url-bar {
            flex: 1;
            background: rgba(40, 40, 40, 0.8);
            border: 1px solid #444;
            color: white;
            padding: 12px 16px;
            border-radius: 12px;
            margin-left: 15px;
            font-size: 13px;
            outline: none;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        .url-bar::placeholder {
            color: #888;
        }
        .url-bar:focus {
            border-color: #667eea;
            box-shadow: 0 0 15px rgba(102, 126, 234, 0.3);
        }
        .browser-content {
            flex: 1;
            background: rgba(10, 10, 10, 0.5);
            position: relative;
            overflow: hidden;
            border-radius: 0 0 12px 0;
        }
        .browser-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: #1a1a1a;
            border-radius: 0 0 12px 0;
        }
        .browser-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #888;
            z-index: 10;
            font-size: 16px;
        }
        .search-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
            color: white;
            padding: 12px 20px;
            font-size: 13px;
            z-index: 100;
            display: none;
            backdrop-filter: blur(10px);
            border-bottom: 2px solid rgba(255, 255, 255, 0.2);
        }
        .search-overlay.show {
            display: block;
            animation: slideDown 0.3s ease;
        }
        @keyframes slideDown {
            from { transform: translateY(-100%); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        .loading {
            display: none;
            text-align: center;
            padding: 12px;
            color: #888;
            font-size: 13px;
            background: rgba(20, 20, 20, 0.8);
            border-radius: 8px;
            margin: 0 20px;
            backdrop-filter: blur(10px);
        }
        .loading.show { display: block; }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Sol Panel - Chat -->
        <div class="chat-panel">
            <div class="chat-header">
                <h1>✨ Manus Computer</h1>
                <p>� Advanced AI Computing Interface</p>
            </div>

            <div class="chat-messages" id="chatMessages">
                <div class="message agent">
                    <div class="message-content">
                        🌟 <strong>Welcome to Manus Computer</strong>
                        <br><br>
                        I'm your advanced AI assistant with powerful capabilities:
                        <br><br>
                        🔍 <strong>Web Research</strong> - Real-time browsing & analysis
                        <br>📊 <strong>Data Science</strong> - Analytics & visualization
                        <br>🎨 <strong>Creative AI</strong> - Content generation & design
                        <br>🧠 <strong>Multi-Model</strong> - Compare different AI models
                        <br>💻 <strong>Code Execution</strong> - Write & run code instantly
                        <br>🔬 <strong>Math & Science</strong> - Complex calculations
                        <br><br>
                        💡 <strong>Try these commands:</strong>
                        <br>• "Research Tesla stock performance"
                        <br>• "Create a Python data visualization"
                        <br>• "Analyze AI technology trends"
                        <br>• "Write and execute a sorting algorithm"
                        <div class="timestamp">System Ready</div>
                    </div>
                </div>
            </div>

            <div class="loading" id="loading">🤔 Düşünüyorum...</div>

            <div class="input-container">
                <form class="input-form" onsubmit="sendMessage(event)">
                    <input type="text" name="message" class="message-input"
                           placeholder="Mesajınızı yazın..." required id="messageInput">
                    <button type="submit" class="send-button" id="sendButton">📤</button>
                </form>
            </div>
        </div>

        <!-- Sağ Panel - Browser -->
        <div class="browser-panel">
            <div class="browser-header">
                <div class="browser-controls">
                    <button class="browser-btn" onclick="browserBack()">◀</button>
                    <button class="browser-btn" onclick="browserForward()">▶</button>
                    <button class="browser-btn" onclick="browserRefresh()">🔄</button>
                    <button class="browser-btn" onclick="browserHome()">🏠</button>
                </div>
                <input type="text" class="url-bar" id="urlBar" placeholder="URL veya arama terimi..."
                       onkeypress="if(event.key==='Enter') navigateToUrl()">
                <button class="browser-btn" onclick="navigateToUrl()">Git</button>
            </div>

            <div class="search-overlay" id="searchOverlay">
                🔍 <span id="searchStatus">Arama yapılıyor...</span>
            </div>

            <div class="browser-content">
                <div class="browser-loading" id="browserLoading">
                    🌐 Tarayıcı hazırlanıyor...<br>
                    <small>Manus AI web araştırması burada görünecek</small>
                </div>
                <iframe class="browser-iframe" id="browserFrame" src="about:blank"></iframe>
            </div>
        </div>
    </div>

    <script>
        let ws = null;

        // WebSocket bağlantısı
        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            ws = new WebSocket(`${protocol}//${window.location.host}/ws`);

            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                if (data.type === 'browser_action') {
                    handleBrowserAction(data);
                } else if (data.type === 'search_status') {
                    updateSearchStatus(data.message);
                } else if (data.type === 'browser_url') {
                    updateBrowserUrl(data.url);
                }
            };

            ws.onclose = function() {
                setTimeout(connectWebSocket, 3000); // Yeniden bağlan
            };
        }

        // Browser aksiyonlarını handle et
        function handleBrowserAction(data) {
            const iframe = document.getElementById('browserFrame');
            const loading = document.getElementById('browserLoading');
            const overlay = document.getElementById('searchOverlay');
            const urlBar = document.getElementById('urlBar');

            if (data.action === 'navigate') {
                loading.style.display = 'none';
                overlay.classList.add('show');
                urlBar.value = data.url;
                iframe.src = data.url;
            } else if (data.action === 'search_complete') {
                overlay.classList.remove('show');
            }
        }

        // Arama durumunu güncelle
        function updateSearchStatus(message) {
            document.getElementById('searchStatus').textContent = message;
        }

        // Browser URL'ini güncelle
        function updateBrowserUrl(url) {
            const iframe = document.getElementById('browserFrame');
            const urlBar = document.getElementById('urlBar');
            const loading = document.getElementById('browserLoading');
            const overlay = document.getElementById('searchOverlay');

            urlBar.value = url;

            // Proxy kullan
            const proxyUrl = '/proxy?url=' + encodeURIComponent(url);
            iframe.src = proxyUrl;

            loading.style.display = 'none';
            overlay.classList.add('show');

            // 2 saniye sonra overlay'i kaldır
            setTimeout(() => {
                overlay.classList.remove('show');
            }, 2000);
        }

        // Mesaj gönder
        async function sendMessage(event) {
            event.preventDefault();

            const messageInput = document.getElementById('messageInput');
            const sendButton = document.getElementById('sendButton');
            const loading = document.getElementById('loading');

            const message = messageInput.value.trim();
            if (!message) return;

            sendButton.disabled = true;
            loading.classList.add('show');
            messageInput.value = '';

            addMessage('user', message);

            try {
                const formData = new FormData();
                formData.append('message', message);

                const response = await fetch('/chat', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    addMessage('agent', data.response);
                } else {
                    addMessage('agent', data.error || 'Bir hata oluştu');
                }

            } catch (error) {
                addMessage('agent', 'Bağlantı hatası: ' + error.message);
            } finally {
                sendButton.disabled = false;
                loading.classList.remove('show');
                messageInput.focus();
            }
        }

        // Mesaj ekle
        function addMessage(type, content) {
            const chatMessages = document.getElementById('chatMessages');
            const timestamp = new Date().toLocaleTimeString('tr-TR',
                {hour: '2-digit', minute: '2-digit'});

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = `
                <div class="message-content">
                    ${content}
                    <div class="timestamp">${timestamp}</div>
                </div>
            `;

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Browser kontrolleri
        function browserBack() {
            document.getElementById('browserFrame').contentWindow.history.back();
        }

        function browserForward() {
            document.getElementById('browserFrame').contentWindow.history.forward();
        }

        function browserRefresh() {
            document.getElementById('browserFrame').contentWindow.location.reload();
        }

        function browserHome() {
            document.getElementById('urlBar').value = 'https://duckduckgo.com';
            navigateToUrl();
        }

        function navigateToUrl() {
            const url = document.getElementById('urlBar').value;
            if (url) {
                let finalUrl = url;
                if (!url.startsWith('http://') && !url.startsWith('https://')) {
                    finalUrl = 'https://duckduckgo.com/?q=' + encodeURIComponent(url);
                }

                // Proxy kullan
                const proxyUrl = '/proxy?url=' + encodeURIComponent(finalUrl);
                document.getElementById('browserFrame').src = proxyUrl;
                document.getElementById('browserLoading').style.display = 'none';
            }
        }

        // Sayfa yüklendiğinde
        window.onload = function() {
            document.getElementById('messageInput').focus();
            connectWebSocket();

            // Varsayılan olarak DuckDuckGo'yu yükle (iframe friendly)
            setTimeout(() => {
                document.getElementById('urlBar').value = 'https://duckduckgo.com';
                navigateToUrl();
            }, 1000);
        };
    </script>
</body>
</html>
    """
    return HTMLResponse(content=html_content)


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint - real-time browser kontrolü"""
    await websocket.accept()
    connected_clients.append(websocket)

    try:
        while True:
            data = await websocket.receive_text()
            # WebSocket mesajlarını handle et

    except WebSocketDisconnect:
        connected_clients.remove(websocket)


async def broadcast_browser_action(action_data):
    """Tüm bağlı istemcilere browser aksiyonu gönder"""
    for client in connected_clients:
        try:
            await client.send_text(action_data)
        except:
            connected_clients.remove(client)


@app.post("/chat")
async def chat(message: str = Form(...)):
    """Chat endpoint - kullanıcı mesajını işle"""
    global chat_history

    if not message.strip():
        return JSONResponse({"error": "Mesaj boş olamaz"}, status_code=400)

    try:
        # Agent'ı al
        agent = get_agent()
        if agent is None:
            return JSONResponse({"error": "Agent başlatılamadı"}, status_code=500)

        # Mesajı işle
        logger.info(f"Kullanıcı mesajı işleniyor: {message}")

        # Agent'ın cevabını al
        response = await process_message_with_agent(agent, message)

        return JSONResponse({"success": True, "response": response})

    except Exception as e:
        error_msg = f"Mesaj işlenirken hata oluştu: {str(e)}"
        logger.error(error_msg)
        return JSONResponse({"error": error_msg}, status_code=500)


@app.get("/proxy")
async def proxy_url(url: str = Query(...)):
    """URL proxy - iframe güvenlik sorunlarını çözer"""
    try:
        # Güvenlik kontrolü
        allowed_domains = [
            "duckduckgo.com",
            "bing.com",
            "yahoo.com",
            "startpage.com",
            "wikipedia.org",
            "github.com",
            "stackoverflow.com",
        ]

        from urllib.parse import urlparse

        parsed = urlparse(url)
        domain = parsed.netloc.lower()

        # Domain kontrolü
        if not any(allowed in domain for allowed in allowed_domains):
            return Response("Domain not allowed", status_code=403)

        # URL'yi fetch et
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }

        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()

        # Content-Type'ı al
        content_type = response.headers.get("content-type", "text/html")

        # HTML ise iframe-friendly hale getir
        if "text/html" in content_type:
            html_content = response.text
            # X-Frame-Options header'ını kaldır
            html_content = html_content.replace(
                "X-Frame-Options", "X-Frame-Options-Disabled"
            )
            return Response(html_content, media_type=content_type)
        else:
            return Response(response.content, media_type=content_type)

    except Exception as e:
        logger.error(f"Proxy error: {e}")
        return Response(f"Proxy error: {str(e)}", status_code=500)


async def process_message_with_agent(agent, message):
    """Agent ile mesajı işle"""
    try:
        # Agent'ın run metodunu çağır
        result = await agent.run(message)

        if result:
            return str(result)
        else:
            return "Üzgünüm, bu konuda size yardımcı olamadım."

    except Exception as e:
        logger.error(f"Agent işleme hatası: {e}")
        return f"İşlem sırasında bir hata oluştu: {str(e)}"


if __name__ == "__main__":
    print("🌐 OpenManus SuperAI - Embedded Browser başlatılıyor...")
    print("📱 Tarayıcınızda http://localhost:8001 adresini açın")
    print("🛑 Durdurmak için Ctrl+C tuşlarına basın")

    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")
