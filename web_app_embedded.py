#!/usr/bin/env python3
# coding: utf-8
"""
OpenManus Web Interface with Embedded Browser
Manus AI'nin web sitesi içinde gömülü tarayıcı ile çalışması
"""

import asyncio
from datetime import datetime
from typing import List

import requests
import uvicorn
from fastapi import FastAPI, Form, Query, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse, JSONResponse, Response
from fastapi.staticfiles import StaticFiles

from app.agent.manus import Manus
from app.logger import logger

app = FastAPI(title="OpenManus SuperAI - Embedded Browser")

# Global değişkenler
current_agent = None
chat_history = []
connected_clients: List[WebSocket] = []


async def broadcast_to_clients(message: str):
    """Tüm bağlı istemcilere mesaj gönder"""
    for client in connected_clients[:]:  # Copy to avoid modification during iteration
        try:
            await client.send_text(message)
        except:
            connected_clients.remove(client)


def get_agent():
    """Agent instance'ını al veya oluştur"""
    global current_agent
    if current_agent is None:
        try:
            current_agent = Manus()

            # Embedded browser tool'una WebSocket callback ekle
            for tool in current_agent.available_tools.tools:
                if hasattr(tool, "websocket_callback"):
                    object.__setattr__(tool, "websocket_callback", broadcast_to_clients)

            logger.info("Agent başarıyla oluşturuldu")
        except Exception as e:
            logger.error(f"Agent oluşturulurken hata: {e}")
            current_agent = None
    return current_agent


@app.get("/", response_class=HTMLResponse)
async def home():
    """Ana sayfa - Embedded Browser ile"""
    html_content = """
<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenManus SuperAI - Embedded Browser</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 10px;
        }
        .main-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 95%;
            max-width: 1600px;
            height: 95vh;
            display: flex;
            overflow: hidden;
        }

        /* Sol Panel - Chat */
        .chat-panel {
            width: 40%;
            display: flex;
            flex-direction: column;
            border-right: 2px solid #e0e0e0;
        }
        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: center;
        }
        .chat-header h1 { font-size: 20px; margin-bottom: 5px; }
        .chat-header p { opacity: 0.9; font-size: 12px; }
        .chat-messages {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        .message {
            margin-bottom: 12px;
            display: flex;
            align-items: flex-start;
        }
        .message.user { justify-content: flex-end; }
        .message-content {
            max-width: 80%;
            padding: 10px 14px;
            border-radius: 15px;
            word-wrap: break-word;
            font-size: 13px;
        }
        .message.user .message-content {
            background: #667eea;
            color: white;
        }
        .message.agent .message-content {
            background: white;
            border: 1px solid #e0e0e0;
            color: #333;
        }
        .timestamp {
            font-size: 10px;
            opacity: 0.7;
            margin-top: 3px;
        }
        .input-container {
            padding: 15px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }
        .input-form {
            display: flex;
            gap: 8px;
        }
        .message-input {
            flex: 1;
            padding: 10px 14px;
            border: 2px solid #e0e0e0;
            border-radius: 20px;
            font-size: 13px;
            outline: none;
        }
        .message-input:focus { border-color: #667eea; }
        .send-button {
            padding: 10px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 13px;
        }

        /* Sağ Panel - Browser */
        .browser-panel {
            width: 60%;
            display: flex;
            flex-direction: column;
            background: #f8f9fa;
        }
        .browser-header {
            background: #343a40;
            color: white;
            padding: 10px 15px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 13px;
        }
        .browser-controls {
            display: flex;
            gap: 5px;
        }
        .browser-btn {
            background: #6c757d;
            border: none;
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
        }
        .browser-btn:hover { background: #5a6268; }
        .url-bar {
            flex: 1;
            background: #495057;
            border: none;
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
            margin-left: 10px;
            font-size: 12px;
        }
        .browser-content {
            flex: 1;
            background: white;
            position: relative;
            overflow: hidden;
        }
        .browser-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }
        .browser-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #6c757d;
            z-index: 10;
        }
        .search-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(52, 58, 64, 0.95);
            color: white;
            padding: 10px 15px;
            font-size: 12px;
            z-index: 100;
            display: none;
            border-bottom: 2px solid #667eea;
        }
        .search-overlay.show { display: block; }
        .loading { display: none; text-align: center; padding: 8px; color: #666; font-size: 12px; }
        .loading.show { display: block; }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Sol Panel - Chat -->
        <div class="chat-panel">
            <div class="chat-header">
                <h1>🚀 OpenManus SuperAI</h1>
                <p>🌍 Embedded Browser AI Assistant</p>
            </div>

            <div class="chat-messages" id="chatMessages">
                <div class="message agent">
                    <div class="message-content">
                        🚀 Merhaba! Ben OpenManus SuperAI!
                        <br><br>
                        🌟 <strong>Özelliklerim:</strong>
                        <br>🔍 • Web araştırması (sağda görürsünüz)
                        <br>📊 • Veri analizi ve görselleştirme
                        <br>🎨 • Yaratıcı içerik üretimi
                        <br>🧠 • Multi-model AI karşılaştırma
                        <br>💻 • Kod yazma ve çalıştırma
                        <br><br>
                        💡 <strong>Örnekler:</strong>
                        <br>• "Python ile hesap makinesi yaz"
                        <br>• "Tesla hisse senedi araştır"
                        <br>• "AI teknolojileri hakkında bilgi"
                        <div class="timestamp">Şimdi</div>
                    </div>
                </div>
            </div>

            <div class="loading" id="loading">🤔 Düşünüyorum...</div>

            <div class="input-container">
                <form class="input-form" onsubmit="sendMessage(event)">
                    <input type="text" name="message" class="message-input"
                           placeholder="Mesajınızı yazın..." required id="messageInput">
                    <button type="submit" class="send-button" id="sendButton">📤</button>
                </form>
            </div>
        </div>

        <!-- Sağ Panel - Browser -->
        <div class="browser-panel">
            <div class="browser-header">
                <div class="browser-controls">
                    <button class="browser-btn" onclick="browserBack()">◀</button>
                    <button class="browser-btn" onclick="browserForward()">▶</button>
                    <button class="browser-btn" onclick="browserRefresh()">🔄</button>
                    <button class="browser-btn" onclick="browserHome()">🏠</button>
                </div>
                <input type="text" class="url-bar" id="urlBar" placeholder="URL veya arama terimi..."
                       onkeypress="if(event.key==='Enter') navigateToUrl()">
                <button class="browser-btn" onclick="navigateToUrl()">Git</button>
            </div>

            <div class="search-overlay" id="searchOverlay">
                🔍 <span id="searchStatus">Arama yapılıyor...</span>
            </div>

            <div class="browser-content">
                <div class="browser-loading" id="browserLoading">
                    🌐 Tarayıcı hazırlanıyor...<br>
                    <small>Manus AI web araştırması burada görünecek</small>
                </div>
                <iframe class="browser-iframe" id="browserFrame" src="about:blank"></iframe>
            </div>
        </div>
    </div>

    <script>
        let ws = null;

        // WebSocket bağlantısı
        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            ws = new WebSocket(`${protocol}//${window.location.host}/ws`);

            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                if (data.type === 'browser_action') {
                    handleBrowserAction(data);
                } else if (data.type === 'search_status') {
                    updateSearchStatus(data.message);
                } else if (data.type === 'browser_url') {
                    updateBrowserUrl(data.url);
                }
            };

            ws.onclose = function() {
                setTimeout(connectWebSocket, 3000); // Yeniden bağlan
            };
        }

        // Browser aksiyonlarını handle et
        function handleBrowserAction(data) {
            const iframe = document.getElementById('browserFrame');
            const loading = document.getElementById('browserLoading');
            const overlay = document.getElementById('searchOverlay');
            const urlBar = document.getElementById('urlBar');

            if (data.action === 'navigate') {
                loading.style.display = 'none';
                overlay.classList.add('show');
                urlBar.value = data.url;
                iframe.src = data.url;
            } else if (data.action === 'search_complete') {
                overlay.classList.remove('show');
            }
        }

        // Arama durumunu güncelle
        function updateSearchStatus(message) {
            document.getElementById('searchStatus').textContent = message;
        }

        // Browser URL'ini güncelle
        function updateBrowserUrl(url) {
            const iframe = document.getElementById('browserFrame');
            const urlBar = document.getElementById('urlBar');
            const loading = document.getElementById('browserLoading');
            const overlay = document.getElementById('searchOverlay');

            urlBar.value = url;

            // Proxy kullan
            const proxyUrl = '/proxy?url=' + encodeURIComponent(url);
            iframe.src = proxyUrl;

            loading.style.display = 'none';
            overlay.classList.add('show');

            // 2 saniye sonra overlay'i kaldır
            setTimeout(() => {
                overlay.classList.remove('show');
            }, 2000);
        }

        // Mesaj gönder
        async function sendMessage(event) {
            event.preventDefault();

            const messageInput = document.getElementById('messageInput');
            const sendButton = document.getElementById('sendButton');
            const loading = document.getElementById('loading');

            const message = messageInput.value.trim();
            if (!message) return;

            sendButton.disabled = true;
            loading.classList.add('show');
            messageInput.value = '';

            addMessage('user', message);

            try {
                const formData = new FormData();
                formData.append('message', message);

                const response = await fetch('/chat', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    addMessage('agent', data.response);
                } else {
                    addMessage('agent', data.error || 'Bir hata oluştu');
                }

            } catch (error) {
                addMessage('agent', 'Bağlantı hatası: ' + error.message);
            } finally {
                sendButton.disabled = false;
                loading.classList.remove('show');
                messageInput.focus();
            }
        }

        // Mesaj ekle
        function addMessage(type, content) {
            const chatMessages = document.getElementById('chatMessages');
            const timestamp = new Date().toLocaleTimeString('tr-TR',
                {hour: '2-digit', minute: '2-digit'});

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = `
                <div class="message-content">
                    ${content}
                    <div class="timestamp">${timestamp}</div>
                </div>
            `;

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Browser kontrolleri
        function browserBack() {
            document.getElementById('browserFrame').contentWindow.history.back();
        }

        function browserForward() {
            document.getElementById('browserFrame').contentWindow.history.forward();
        }

        function browserRefresh() {
            document.getElementById('browserFrame').contentWindow.location.reload();
        }

        function browserHome() {
            document.getElementById('urlBar').value = 'https://duckduckgo.com';
            navigateToUrl();
        }

        function navigateToUrl() {
            const url = document.getElementById('urlBar').value;
            if (url) {
                let finalUrl = url;
                if (!url.startsWith('http://') && !url.startsWith('https://')) {
                    finalUrl = 'https://duckduckgo.com/?q=' + encodeURIComponent(url);
                }

                // Proxy kullan
                const proxyUrl = '/proxy?url=' + encodeURIComponent(finalUrl);
                document.getElementById('browserFrame').src = proxyUrl;
                document.getElementById('browserLoading').style.display = 'none';
            }
        }

        // Sayfa yüklendiğinde
        window.onload = function() {
            document.getElementById('messageInput').focus();
            connectWebSocket();

            // Varsayılan olarak DuckDuckGo'yu yükle (iframe friendly)
            setTimeout(() => {
                document.getElementById('urlBar').value = 'https://duckduckgo.com';
                navigateToUrl();
            }, 1000);
        };
    </script>
</body>
</html>
    """
    return HTMLResponse(content=html_content)


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint - real-time browser kontrolü"""
    await websocket.accept()
    connected_clients.append(websocket)

    try:
        while True:
            data = await websocket.receive_text()
            # WebSocket mesajlarını handle et

    except WebSocketDisconnect:
        connected_clients.remove(websocket)


async def broadcast_browser_action(action_data):
    """Tüm bağlı istemcilere browser aksiyonu gönder"""
    for client in connected_clients:
        try:
            await client.send_text(action_data)
        except:
            connected_clients.remove(client)


@app.post("/chat")
async def chat(message: str = Form(...)):
    """Chat endpoint - kullanıcı mesajını işle"""
    global chat_history

    if not message.strip():
        return JSONResponse({"error": "Mesaj boş olamaz"}, status_code=400)

    try:
        # Agent'ı al
        agent = get_agent()
        if agent is None:
            return JSONResponse({"error": "Agent başlatılamadı"}, status_code=500)

        # Mesajı işle
        logger.info(f"Kullanıcı mesajı işleniyor: {message}")

        # Agent'ın cevabını al
        response = await process_message_with_agent(agent, message)

        return JSONResponse({"success": True, "response": response})

    except Exception as e:
        error_msg = f"Mesaj işlenirken hata oluştu: {str(e)}"
        logger.error(error_msg)
        return JSONResponse({"error": error_msg}, status_code=500)


@app.get("/proxy")
async def proxy_url(url: str = Query(...)):
    """URL proxy - iframe güvenlik sorunlarını çözer"""
    try:
        # Güvenlik kontrolü
        allowed_domains = [
            "duckduckgo.com",
            "bing.com",
            "yahoo.com",
            "startpage.com",
            "wikipedia.org",
            "github.com",
            "stackoverflow.com",
        ]

        from urllib.parse import urlparse

        parsed = urlparse(url)
        domain = parsed.netloc.lower()

        # Domain kontrolü
        if not any(allowed in domain for allowed in allowed_domains):
            return Response("Domain not allowed", status_code=403)

        # URL'yi fetch et
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }

        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()

        # Content-Type'ı al
        content_type = response.headers.get("content-type", "text/html")

        # HTML ise iframe-friendly hale getir
        if "text/html" in content_type:
            html_content = response.text
            # X-Frame-Options header'ını kaldır
            html_content = html_content.replace(
                "X-Frame-Options", "X-Frame-Options-Disabled"
            )
            return Response(html_content, media_type=content_type)
        else:
            return Response(response.content, media_type=content_type)

    except Exception as e:
        logger.error(f"Proxy error: {e}")
        return Response(f"Proxy error: {str(e)}", status_code=500)


async def process_message_with_agent(agent, message):
    """Agent ile mesajı işle"""
    try:
        # Agent'ın run metodunu çağır
        result = await agent.run(message)

        if result:
            return str(result)
        else:
            return "Üzgünüm, bu konuda size yardımcı olamadım."

    except Exception as e:
        logger.error(f"Agent işleme hatası: {e}")
        return f"İşlem sırasında bir hata oluştu: {str(e)}"


if __name__ == "__main__":
    print("🌐 OpenManus SuperAI - Embedded Browser başlatılıyor...")
    print("📱 Tarayıcınızda http://localhost:8001 adresini açın")
    print("🛑 Durdurmak için Ctrl+C tuşlarına basın")

    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")
